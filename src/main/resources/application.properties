server.port=8090

spring.application.name=CorpUser
logging.level.tech.business-banking.lite.sample=INFO
jhlite-hidden-resources.tags=banner
#spring.jackson.default-property-inclusion=non_null
spring.main.allow-bean-definition-overriding=true
# Disable the modules and its dependencies by slugs
jhlite-hidden-resources.slugs=custom-jhlite
management.endpoints.web.base-path=/management
management.endpoints.web.exposure.include=configprops,env,health,info,logfile,loggers,threaddump
management.endpoint.health.probes.enabled=true
management.endpoint.health.show-details=always
spring.task.scheduling.thread-name-prefix=litesample-scheduling-
spring.task.execution.pool.keep-alive=10s
spring.task.execution.pool.queue-capacity=100
spring.task.scheduling.pool.size=2
spring.task.execution.pool.max-size=16
spring.task.execution.thread-name-prefix=litesample-task-
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

spring.codec.max-in-memory-size=500KB

#kafka.producer.'[key.serializer]'=org.apache.kafka.common.serialization.StringSerializer
#kafka.bootstrap-servers=localhost:9092
#kafka.consumer.'[value.deserializer]'=org.apache.kafka.common.serialization.StringDeserializer
#kafka.polling.timeout=10000
#kafka.consumer.'[auto.offset.reset]'=earliest
#kafka.producer.'[value.serializer]'=org.apache.kafka.common.serialization.StringSerializer
#kafka.consumer.'[key.deserializer]'=org.apache.kafka.common.serialization.StringDeserializer
#kafka.consumer.'[group.id]'=myapp
#kafka.topic.dummy=queue.Customer.dummy
spring.liquibase.change-log=classpath:config/liquibase/master.xml

efw.core.openapi.enabled=true
spring.profiles.active=dev

#application.security.jwt-base64-secret=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiCj9t7ciXduOB1ZoylcjbuIi5xvK4B2uzM92QKcSikHxuXDhFgTPPFtV5kZxR0XLV82LM7dhRz6GKJFfW6Q3it4Dbq+imKhdK/vik2itXOs40L6Gq5i6t9Y5zVxOxeTxTqsrll5F6uDwdgB83PLTRL6ysplHyq3BE0HyZFPJtB+WIp/Vn8Gmr/esSmcVLKXltz98jlpPCaJVxrvI08cvj/L2pNHY5wTcEfR1CdXI5JKtGaiJgu7FhwdqWbNlr07933VR6HCNRwCid5O7GoQhy0q67RSkZH7pD8If6Vcr2bdl7JDGF2Xbk4tAOVwG2H7v240ug50oWi1Hdg5JNXA0vwIDAQAB

keycloak.business.auth-server-url=https://keycloak-test.int.bic.tech
keycloak.business.realm=business-banking
keycloak.business.resource=customer-management
keycloak.business.credentials.secret=AdXKEBxBPLBk5m69t7PPa3ibPXBYCXba

keycloak.business.employee.auth-server-url=https://keycloak-test.int.bic.tech
keycloak.business.employee.realm=business-employee-portal
keycloak.business.employee.resource=customer-management
keycloak.business.employee.credentials.secret=jhtUfYC7RSwElepYLUOYBhjA4F0mryqD

keycloak.retail.auth-server-url=https://keycloak-test.int.bic.tech
keycloak.retail.realm=retail-banking
keycloak.retail.resource=customer-management
keycloak.retail.credentials.secret=NKAr3rnjwm9jlakgKpelukZGFaHYqIWE
