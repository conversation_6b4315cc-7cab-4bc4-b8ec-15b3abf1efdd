server.port=8090

# Swagger setting
springdoc.swagger-ui.path=/swagger-ui.html

logging.level.tech.jhipster.lite.sample=DEBUG
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Git Information
management.info.git.mode=full
management.info.env.enabled=true
management.info.git.enabled=true
spring.datasource.hikari.poolName=Hikari
spring.data.jpa.repositories.bootstrap-mode=deferred
spring.jpa.properties.hibernate.jdbc.time_zone=UTC
spring.jpa.properties.hibernate.query.fail_on_pagination_over_collection_fetch=true
spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.hibernate.naming.implicit-strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy
spring.jpa.open-in-view=false
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.jpa.properties.hibernate.connection.provider_disables_autocommit=true
spring.jpa.properties.hibernate.order_inserts=true
spring.datasource.hikari.auto-commit=false
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.jpa.properties.hibernate.query.in_clause_parameter_padding=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

keycloak.endpoint.baseUrl=https://keycloak-test.bic.tech
keycloak.endpoint.clientId=admin-cli
keycloak.endpoint.username=admin
keycloak.endpoint.password=s54T5kcjfQc77n!@
keycloak.endpoint.grantType=password
keycloak.endpoint.realmMaster=master
keycloak.endpoint.realmCDB=business-banking
keycloak.endpoint.realmBEP=business-employee-portal




#keycloak.endpoint.baseUrl=http://localhost:9080
#keycloak.endpoint.clientId=admin-cli
#keycloak.endpoint.username=admin
#keycloak.endpoint.password=admin
#keycloak.endpoint.grantType=password
#keycloak.endpoint.realmMaster=master
#keycloak.endpoint.realmCDB=business-banking
#keycloak.endpoint.realmBEP=business-banking

#spring.jackson.default-property-inclusion=non_null

# realm business-banking
#application.security.jwt-base64-secret=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnKtKWn/KxYvIDIDx+EUfhBcUekUBiJpp9V0rqyIxYFS1mImUi34e0ibvSi8OE3VMGZgwBjrtC0VVL6kTj1KUkLSQLP/I6SBTK6jAUEvyU3ZSsFJ8gRxzGtora7+hVQv3BuE2ZthOl6q5IX9PikYab7T72zRa139CnO2R+KNhGT3cbfpAU2K2nLBC3pLwqGL+wOsLmS4LNwPPwS+ysHiMOHw17+1bZmrfFEqgu0SAkqwEi3PRsivQe8Xq/FZH26G3vFpy5vxS+TB0+s2kNjlm1hsUtPw10wBO5e4mAEmFyDOhsMo2qSRttxM9ScXEa/91rMZGvhIQPXRkRWHboU+4+wIDAQAB

# realm business-employee-portal
application.security.jwt-base64-secret=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw3dk3ZdHOIrDMyz6NzFR2s8FnKpa+fIRIzzMiXeGVrvLct51mBEBq7WCeJ2luD7uNnM4ZJWPPpGqK6cVnb+uBX7bc/SSqOUyMECyy0Zs0Y2mkDDoTG095QGFEcK0Or1W22/gONUkq06udRDbuC861w7IiwI0pAht4QvblkTPEYN0Mu1C5FXT32C3ZX0IpZ+ukIlB0vOEEUJXv843+mJaXqb8MITZp9CH4+qm1msWE0I1ljAOvFNT2mu3/EgDp9f5uzS+wMOaVMg+PPTOhRQ1gNFtULftOW0j6s4ih5hnCKIspqYTMvyFR15+FHjPq/ZpyYqZotM5MARwjuFfb61XtQIDAQAB


#service_config
customer-service.internal.endpoint.baseUrl=http://customer-profile.dev.int.bic.tech
customer-service.internal.endpoint.getCustomerById=/api/internal/customers/information

approval-service.internal.endpoint.baseUrl=https://bep-approval.dev.int.bic.tech
approval-service.internal.endpoint.createApproval=/internal/api/v3/approval/requests


# Access control
access-control-service.internal.endpoint.baseUrl=https://corp-access-control.dev.int.bic.tech
access-control-service.internal.endpoint.checkIfPermit=/api/internal/access-control/role/{userId}/{bf_code}/{action_type}
access-control-service.internal.endpoint.getCallBackConfigByBfCode=/api/access-control/business-function/{bf_code}
access-control-service.internal.endpoint.getBusinessFunctions=/api/internal/access-control/{userId}/business-functions
access-control-service.internal.endpoint.getBfByUserIdAndBfCode=/api/internal/access-control/roles/{userId}/{bfCode}
access-control-service.internal.endpoint.assignJobRole=/api/internal/access-control/job-role/assign
access-control-service.internal.endpoint.assignJobRolesUsers=/api/internal/access-control/job-role/assign-to-users
access-control-service.internal.endpoint.getBfSettings=/api/internal/access-control/bf/setting
access-control-service.internal.endpoint.getUsersByConditions=/api/internal/access-control/job-role/users/query

# User endpoint
user.identity.endpoint=http://corp-user.dev.int.bic.tech
user.identity.maxVerifyPasswordAttempt=3

# Confirmation
confirmation.endpoint=https://confirmation-service.dev.int.bic.tech

# Microsoft
microsoft.tenant=0ba46404-9b2e-4336-86e2-df512857c568
microsoft.url=https://login.microsoftonline.com
microsoft.client_id=958304ac-3241-4043-bc88-7c0cdc40aa0e
microsoft.client_secret=****************************************
microsoft.resource=https://graph.microsoft.com
microsoft.grant_type=client_credentials


# Device
device.endpoint=https://device-management-service.dev.int.bic.tech

#approval
approval.endpoint=https://bep-approval.dev.int.bic.tech/internal/api/v2/approval/requests
approval.callBackUrlEnroll=https://corp-user.dev.int.bic.tech/internal/api/bep/users/management/callback/enroll-user
approval.callBackUrlSuspend=https://corp-user.dev.int.bic.tech/internal/api/bep/users/management/callback/suspend-user
approval.callBackUrlActive=https://corp-user.dev.int.bic.tech/internal/api/bep/users/management/callback/active-user




