<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd"
>
  <changeSet id="cdb_user_schema" author="luke">
    <createTable tableName="cdb_user">
      <column name="id" type="varchar(40)">
        <constraints nullable="false" primaryKey="true" />
      </column>

      <column name="customer_id" type="varchar(60)">
      </column>

      <column name="idp_user_id" type="varchar(50)">
        <constraints nullable="false" />
      </column>

      <column name="status" type="varchar(15)" defaultValue="ACTIVE">
        <constraints nullable="false" />
      </column>

      <column name="created_by" type="varchar(255)">
      </column>

      <column name="created_date" type="datetime">
      </column>

      <column name="last_modified_by" type="varchar(255)">
      </column>

      <column name="last_modified_date" type="datetime">
      </column>

      <column name="code" type="varchar(255)">
      </column>

    </createTable>


  </changeSet>
</databaseChangeLog>
