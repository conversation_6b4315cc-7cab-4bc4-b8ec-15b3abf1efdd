<?xml version="1.0" encoding="utf-8" ?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
>
  <property name="now" value="now()" dbms="h2" />
  <property name="now" value="current_timestamp" dbms="postgresql" />
  <property name="floatType" value="float4" dbms="postgresql, h2" />
  <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb" />
  <property name="clobType" value="longvarchar" dbms="h2" />
  <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql" />
  <property name="uuidType" value="uuid" dbms="h2, postgresql" />
  <property name="uuidType" value="binary(16)" dbms="mysql, oracle, mssql, mariadb" />
  <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb" />
  <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2" />

  <!-- jhipster-needle-liquibase-add-changelog -->
  <include file="config/liquibase/changelog/0000000001_create_table_cdb_user.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000002_create_table_bep_user.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000003_bep_user_add_column.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000004_bep_user_add_column_employee.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000005_bep_user_add_column_branch.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000006_bep_user_add_column_branch_code_department_code.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000007_bep_user_add_column_jobRoleIds.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000008_bep_user_change_column_jobRoleIds.xml" relativeToChangelogFile="false" />
  <include file="config/liquibase/changelog/0000000009_add_transaction_table.xml" relativeToChangelogFile="false" />
</databaseChangeLog>
