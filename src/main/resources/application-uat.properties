server.port=8090

# Swagger setting
springdoc.swagger-ui.path=/swagger-ui.html



#service_config
customer-service.internal.endpoint.baseUrl=http://customer-profile.uat.int.bic.tech
customer-service.internal.endpoint.getCustomerById=/api/internal/customers/information

approval-service.internal.endpoint.baseUrl=https://bep-approval.uat.int.bic.tech
approval-service.internal.endpoint.createApproval=/internal/api/v2/approval/requests


# Access control
access-control-service.internal.endpoint.baseUrl=https://corp-access-control.uat.int.bic.tech
access-control-service.internal.endpoint.checkIfPermit=/api/internal/access-control/role/{userId}/{bf_code}/{action_type}
access-control-service.internal.endpoint.getCallBackConfigByBfCode=/api/access-control/business-function/{bf_code}
access-control-service.internal.endpoint.getBusinessFunctions=/api/internal/access-control/{userId}/business-functions
access-control-service.internal.endpoint.getBfByUserIdAndBfCode=/api/internal/access-control/roles/{userId}/{bfCode}
access-control-service.internal.endpoint.assignJobRole=/api/internal/access-control/job-role/assign
access-control-service.internal.endpoint.assignJobRolesUsers=/api/internal/access-control/job-role/assign-to-users
access-control-service.internal.endpoint.getBfSettings=/api/internal/access-control/bf/setting
access-control-service.internal.endpoint.getUsersByConditions=/api/internal/access-control/job-role/users/query

# User endpoint
user.identity.endpoint=http://corp-user.uat.int.bic.tech

# Confirmation
confirmation.endpoint=https://confirmation-service.uat.int.bic.tech

# Microsoft
microsoft.tenant=0ba46404-9b2e-4336-86e2-df512857c568
microsoft.url=https://login.microsoftonline.com
microsoft.client_id=958304ac-3241-4043-bc88-7c0cdc40aa0e
microsoft.client_secret=****************************************
microsoft.resource=https://graph.microsoft.com
microsoft.grant_type=client_credentials


# Device
device.endpoint=https://device-management-service.uat.int.bic.tech

#approval
approval.endpoint=https://bep-approval.uat.int.bic.tech/internal/api/v2/approval/requests
approval.callBackUrlEnroll=https://corp-user.uat.int.bic.tech/internal/api/bep/users/management/callback/enroll-user
approval.callBackUrlSuspend=https://corp-user.uat.int.bic.tech/internal/api/bep/users/management/callback/suspend-user
approval.callBackUrlActive=https://corp-user.uat.int.bic.tech/internal/api/bep/users/management/callback/active-user