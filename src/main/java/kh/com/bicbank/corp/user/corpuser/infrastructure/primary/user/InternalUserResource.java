package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import com.bic.efw.core.exception.ForbiddenException;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import kh.com.bicbank.corp.user.account.domain.Account;
import kh.com.bicbank.corp.user.approval.application.ApprovalService;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFActionType;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@Tag(name = "Users Internal API")
@RequiredArgsConstructor
@Hidden
@Slf4j
@RequestMapping("/api/internal/v1/corp/users")
public class InternalUserResource {

    private final UserService userService;
    private final ApprovalService approvalService;

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUser.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/{userId}")
    ResponseEntity<Response> getUserDetail(@PathVariable String userId) {
        log.info("getUserDetail: {}", userId);
        return ResponseEntity.ok(userService.getCorpUserDetail(userId));
    }

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUser.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/profile/{userId}")
    ResponseEntity<Response> getUserProfile(@PathVariable String userId) {
        log.info("getUserDetail: {}", userId);
        return ResponseEntity.ok(userService.getUserInfoDetail(userId));
    }

    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    content = {
                            @Content(
                                    schema = @Schema(implementation = RestUser.class),
                                    mediaType = "application/json")
                    })
    })
    @GetMapping("/list-user-belong-customer")
    ResponseEntity<Response> getUserListBelongCustomerId(
            @RequestParam String customerId,
            @RequestParam(required = false, defaultValue = "") String search,
            @RequestParam(required = false, defaultValue = "") String userName,
            @RequestParam(required = false, defaultValue = "") String email,
            @RequestParam(required = false, defaultValue = "") String idNumber,
            @RequestParam(required = false, defaultValue = "") String phoneNumber,
            @RequestParam(required = false, defaultValue = "")  String status) {

        return ResponseEntity.ok(
                userService.getUserListOfCustomerId(
                        customerId,
                        search,
                        userName,
                        email,
                        idNumber,
                        phoneNumber,
                        status));
    }

    @Operation(summary = "List corp users")
    @GetMapping
    ResponseEntity<Response> getUserList(
        @RequestParam String customerId,
        @RequestParam(required = false, defaultValue = "") String search,
        @RequestParam(required = false, defaultValue = "") String userName,
        @RequestParam(required = false, defaultValue = "") String email,
        @RequestParam(required = false, defaultValue = "") String idNumber,
        @RequestParam(required = false, defaultValue = "") String phoneNumber,
        @RequestParam(required = false, defaultValue = "")  String status,
        RestCustomPageable customPageable) {
        return ResponseEntity.ok(
            userService.getUserList(
                customerId,
                search,
                userName,
                email,
                idNumber,
                phoneNumber,
                status,
                customPageable));
    }

    @Operation(summary = "Create a new corp user")
    @PostMapping
    ResponseEntity<Response> createUser(
        @RequestBody RestUserToCreate request) {
        return ResponseEntity.ok(new Response(true, approvalService.postApprovalCreateUser(account, request), null));
    }
}
