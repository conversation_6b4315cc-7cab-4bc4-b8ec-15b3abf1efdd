package kh.com.bicbank.corp.user.corpuser.domain.bep;

import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import kh.com.bicbank.corp.user.shared.datetime.infrastructure.DatetimeUtils;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakAttributesEnum;
import kh.com.bicbank.corp.user.shared.rest.domain.User;
import kh.com.bicbank.corp.user.shared.rest.domain.UserAttribute;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@Builder
public class UserBepResponse {
    private String userId;
    private String userName;
    private String firstName;
    private String lastName;
    private String firstNameKhr;
    private String lastNameKhr;
    private String identifyType;
    private String idNumber;
    private String dob;
    private String gender;
    private String phoneNumber;
    private String address;
    private String email;
    private String jobTitle;
    private String jobRoleId;
    private String description;
    private String issuedDate;
    private String expiredDate;
    private String createdAt;
    private String lastLogin;
    private String status;
    private String department;
    private String employeeId;
    private String branch;
    private String branchCode;
    private String departmentCode;
    private String jobRoleIds;
    public static UserBepResponse from(User user) {
        String userId = user.getId();
        String userName = user.getUsername();
        String firstName = user.getFirstName();
        String lastName = user.getLastName();
        String email = user.getEmail();
        String createdAt = new DatetimeUtils().convertToUTCDate(user.getCreatedTimestamp());
        String lastLogin = null;
        String phoneNumber = null;
        String address = null;
        String firstNameKhr = null;
        String lastNameKhr = null;
        String identifyType = null;
        String idNumber = null;
        String dob = null;
        String gender = null;
        String jobTitle = null;
        String jobRoleId = null;
        String description = null;
        String issuedDate = null;
        String expiredDate = null;
        String oid = null;
        String tid = null;
        String status = user.getEnabled() ? UserStatus.ACTIVE.name() : UserStatus.LOCKED.name();

        List<UserAttribute> attributes = user.getAttributes();
        if (!attributes.isEmpty()) {
            for (UserAttribute attribute : attributes) {
                switch (attribute.getName()) {
                    case KeycloakAttributesEnum.OID_ATTRIBUTE:
                        oid = attribute.getValue();
                        break;
                    case KeycloakAttributesEnum.TID_ATTRIBUTE:
                        tid = attribute.getValue();
                        break;
                    default:
                        break;
                }
            }
        }

        return UserBepResponse.builder()
                .userId(userId)
                .userName(userName)
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .firstNameKhr(firstNameKhr)
                .lastNameKhr(lastNameKhr)
                .createdAt(createdAt)
                .identifyType(identifyType)
                .idNumber(idNumber)
                .dob(dob)
                .gender(gender)
                .jobTitle(jobTitle)
                .jobRoleId(jobRoleId)
                .phoneNumber(phoneNumber)
                .address(address)
                .description(description)
                .issuedDate(issuedDate)
                .expiredDate(expiredDate)
                .lastLogin(lastLogin)
                .status(status)
                .build();
    }
    public static UserBepResponse from(UserBepEntity user) {
        String userId = user.getId();
        String userName = user.getDisplayName();
        String firstName = user.getSurname();
        String lastName = user.getGivenName();
        String email = user.getMail();
        String createdAt = user.getCreatedDateTime();
        String lastLogin = null;
        String phoneNumber = user.getMobilePhone();
        String address = null;
        String firstNameKhr = null;
        String lastNameKhr = null;
        String identifyType = null;
        String idNumber = null;
        String dob = null;
        String gender = null;
        String jobTitle = user.getJobTitle();
        String jobRoleId = null;
        String description = null;
        String issuedDate = null;
        String expiredDate = null;
        String oid = null;
        String tid = null;
        String department = user.getDepartment();
        String employeeId = user.getEmployeeId();
        String status = user.getStatus() != null ? user.getStatus().name() : null;
        String branch = user.getBranch() != null ? user.getBranch() : null;
        String branchCode = user.getBranchCode() != null ? user.getBranchCode() : null;
        String departmentCode = user.getDepartmentCode() != null ? user.getDepartmentCode() : null;
        String jobRoleIds = user.getJobRoleIds() != null ? user.getJobRoleIds() : null;

        return UserBepResponse.builder()
                .userId(userId)
                .userName(userName)
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .firstNameKhr(firstNameKhr)
                .lastNameKhr(lastNameKhr)
                .createdAt(createdAt)
                .identifyType(identifyType)
                .idNumber(idNumber)
                .dob(dob)
                .gender(gender)
                .jobTitle(jobTitle)
                .jobRoleId(jobRoleId)
                .phoneNumber(phoneNumber)
                .address(address)
                .description(description)
                .issuedDate(issuedDate)
                .expiredDate(expiredDate)
                .lastLogin(lastLogin)
                .status(status)
                .department(department)
                .employeeId(employeeId)
                .branch(branch)
                .branchCode(branchCode)
                .departmentCode(departmentCode)
                .jobRoleIds(jobRoleIds)
                .build();
    }

}
