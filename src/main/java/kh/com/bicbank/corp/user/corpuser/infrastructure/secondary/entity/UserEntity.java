package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity;

import jakarta.persistence.*;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserToCreate;
import kh.com.bicbank.corp.user.shared.error.domain.Assert;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "cdb_user")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  private String id;

  @Column(name = "customer_id")
  private String customerId;

  @Column(name = "idp_user_id")
  private String idpUserId;

  @Enumerated(value = EnumType.STRING)
  @Column(name = "status")
  private UserStatus status;

  @Column(name = "created_by")
  private String createdBy;

  @Column(name = "created_date")
  private LocalDateTime createdDate;

  @Column(name = "last_modified_by")
  private String lastModifiedBy;

  @Column(name = "last_modified_date")
  private LocalDateTime lastModifiedDate;

  @Column(name = "code")
  private String code;

  public static UserEntity from(String userId, UserToCreate userDetail) {
    Assert.notNull("UserDetail", userDetail);
    return UserEntity
      .builder()
      .customerId(userDetail.getCustomerId())
      .idpUserId(userId)
      .status(UserStatus.ACTIVE)
      .createdDate(LocalDateTime.now())
      .build();
  }

}
