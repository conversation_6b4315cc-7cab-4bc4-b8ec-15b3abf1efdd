package kh.com.bicbank.corp.user.corpuser.domain.access_control;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssignJobRole {
  @NotNull
  private UUID userId;

  @NotNull
  private String customerId;

  @NotBlank
  private List<UUID> jobRoleIds;
}
