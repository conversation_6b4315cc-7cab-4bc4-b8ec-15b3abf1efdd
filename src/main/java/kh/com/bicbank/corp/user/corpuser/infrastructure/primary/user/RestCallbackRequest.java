package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@Setter
@ToString
public class RestCallbackRequest {
  @JsonProperty("transactionData")
  private Object transactionData;

  @JsonProperty("status")
  private String status;

  @JsonProperty("checker")
  private UUID checker;
}
