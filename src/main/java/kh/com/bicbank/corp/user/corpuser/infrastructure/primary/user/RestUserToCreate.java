package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import static kh.com.bicbank.corp.user.shared.constant.domain.PatternConstant.User.USERNAME_REGEX;

@Schema(name = "userToCreate", description = "An user to create")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RestUserToCreate {
  @NotEmpty
  @Length(min = 1, max = 50)
  private String customerId;

  private String customerName;

  private String remark;

  private Object additionalData;

  @NotEmpty
  @Length(min = 4, max = 25)
  @Pattern(regexp = USERNAME_REGEX)
  private String userName;

  @NotEmpty
  @Length(min = 1, max = 100)
  private String firstName;

  @NotEmpty
  @Length(min = 1, max = 100)
  private String lastName;

  @Length(max = 100)
  private String firstNameKhr;

  @Length(max = 100)
  private String lastNameKhr;

  @Length(max = 50)
  private String identifyType;

  @NotEmpty
  @Length(min = 1, max = 25)
  private String idNumber;

  @NotEmpty
  private String dob;

  @NotEmpty
  @Length(min = 1, max = 5)
  private String gender;

  @NotEmpty
  @Length(min = 1, max = 20)
  private String phoneNumber;

  @Length(min = 1, max = 200)
  private String address;

  @NotEmpty
  @Length(min = 1, max = 50)
  @Email
  private String email;

  @Length(max = 200)
  private String jobTitle;

  @NotEmpty
  private String jobRoleId;

  @Length(max = 500)
  private String description;

  @NotEmpty
  private String issuedDate;

  @NotEmpty
  private String expiredDate;

}
