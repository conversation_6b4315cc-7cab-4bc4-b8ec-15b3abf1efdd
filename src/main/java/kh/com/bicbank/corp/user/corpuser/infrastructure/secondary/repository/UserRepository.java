package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository;

import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserEntity;

import java.util.List;

public interface UserRepository {

    UserEntity save(UserEntity entity);

    void updateStatusByIdpUserId(String idpUserId, UserStatus status);

    List<UserEntity> findByIdpUserId(String id);

    UserEntity findById(String id);
}
