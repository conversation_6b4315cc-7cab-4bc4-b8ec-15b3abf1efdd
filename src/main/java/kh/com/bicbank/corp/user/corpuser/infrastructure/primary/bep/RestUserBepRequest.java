package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.bep;

import com.bic.efw.core.error.Assert;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepClientRequestDto;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepTransactionDataDto;
import kh.com.bicbank.corp.user.shared.enumeration.domain.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "EnrollUserBepRequest", description = "Enroll user bep")
public class RestUserBepRequest {

    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("userType")
    private UserType userType = UserType.BEP;

    @JsonProperty("idpIssuer")
    private String idpIssuer;

    @JsonProperty("bfCode")
    private String bfCode;

    @JsonProperty("bfName")
    private String bfName;

    @JsonProperty("remark")
    @Size(max = 500)
    private String remark;

    @JsonProperty("transactionData")
    private List<UserBepTransactionDataDto> transactionData;

    @JsonProperty("callBackUrl")
    private String callBackUrl;

    @JsonProperty("bfgId")
    private String bfgId;

    @JsonProperty("bfgName")
    private String bfgName;

    @JsonProperty("bfgCode")
    private String bfgCode;

    @JsonProperty("additionalData")
    private Object additionalData;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("branchCode")
    private String branchCode;

    @JsonProperty("departmentCode")
    private String departmentCode;


    public UserBepClientRequestDto toDomain(UUID userId, String username) {
     /*   Assert.notNull("customerId", customerId);*/
        Assert.notNull("userId", userId);
        Assert.notNull("userType", userType);
        Assert.notNull("bfCode", bfCode);
        Assert.notNull("idpIssuer", idpIssuer);
        Assert.notNull("transactionData", transactionData);

        return UserBepClientRequestDto.builder()
                .customerId(customerId)
                .customerName(customerName)
                .userId(userId)
                .username(username)
                .userType(userType)
                .idpIssuer(idpIssuer)
                .bfCode(bfCode)
                .bfName(bfName)
                .remark(remark)
                .transactionData(transactionData)
                .callbackUrl(callBackUrl)
                .bfgId(bfgId)
                .bfgCode(bfgCode)
                .bfgName(bfgName)
                .additionalData(additionalData)
                .fullName(fullName)
                .branchCode(branchCode)
                .departmentCode(departmentCode)
                .build();
    }
}
