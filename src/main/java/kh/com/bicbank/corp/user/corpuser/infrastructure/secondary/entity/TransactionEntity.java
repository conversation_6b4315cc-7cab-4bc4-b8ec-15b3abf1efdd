package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity;

import com.bic.efw.core.error.Assert;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import kh.com.bicbank.corp.user.corpuser.domain.user.TransactionDataDto;
import kh.com.bicbank.corp.user.corpuser.domain.user.TransactionDto;
import kh.com.bicbank.corp.user.shared.enumeration.domain.TransactionStatus;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import java.util.UUID;

@Setter
@Getter
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "transaction")
public class TransactionEntity {

    @Id
    private UUID id;

    @Type(JsonType.class)
    @Column(columnDefinition = "json")
    private Object request;

    @Type(JsonType.class)
    @Column(columnDefinition = "json")
    private TransactionDataDto data;

    @Temporal(TemporalType.TIMESTAMP)
    @CreatedDate
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createdDate = LocalDateTime.now();

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime processedDate;

    @Enumerated(EnumType.STRING)
    private TransactionStatus status = TransactionStatus.DRAFT;

    @Temporal(TemporalType.TIMESTAMP)
    @LastModifiedDate
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime lastModifiedDate = LocalDateTime.now();

    public static TransactionEntity from(TransactionDto transactionDto) {
        Assert.notNull("transactionDto", transactionDto);
        return TransactionEntity.builder()
                .id(transactionDto.getId())
                .request(transactionDto.getRequest())
                .data(transactionDto.getData())
                .createdDate(transactionDto.getCreatedDate())
                .processedDate(transactionDto.getProcessedDate())
                .status(transactionDto.getStatus())
                .lastModifiedDate(transactionDto.getLastModifiedDate())
                .build();
    }

    public TransactionDto toDomain() {
        return TransactionDto.builder()
                .id(id)
                .request(request)
                .data(data)
                .createdDate(createdDate)
                .processedDate(processedDate)
                .status(status)
                .lastModifiedDate(lastModifiedDate)
                .build();
    }
}
