package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import com.bic.efw.core.payload.Response;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kh.com.bicbank.corp.user.corpuser.application.CallbackService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Tag(name = "Internal Users Receive Callback API")
@RequestMapping("/internal/api/v1/callback/users")
@RequiredArgsConstructor
@Hidden
public class InternalCallbackResource {

  private final CallbackService callbackService;

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        })
  })
  @PostMapping("")
  ResponseEntity<Response> createUser(@RequestBody @Validated RestCallbackRequest request) {
    return ResponseEntity.ok(callbackService.createUser(request));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/update")
  ResponseEntity<Response> updateUserInfo(
      @PathVariable String userId, @RequestBody @Validated RestCallbackRequest request) {
    return ResponseEntity.ok(callbackService.updateUserInformation(userId, request));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/status")
  ResponseEntity<Response> updateUserStatus(
      @PathVariable String userId, @RequestBody @Validated RestCallbackRequest request) {
    return ResponseEntity.ok(callbackService.updateUserStatus(userId, request));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/reset-password")
  ResponseEntity<Response> resetPassword(
      @PathVariable String userId, @RequestBody RestCallbackRequest request) {
    return ResponseEntity.ok(callbackService.resetPassword(userId, request));
  }
}
