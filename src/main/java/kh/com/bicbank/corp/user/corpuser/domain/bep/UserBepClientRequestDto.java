package kh.com.bicbank.corp.user.corpuser.domain.bep;


import kh.com.bicbank.corp.user.corpuser.domain.client.approval.ApprovalClientCreateDataRequestDto;
import kh.com.bicbank.corp.user.shared.enumeration.domain.CallBackMethod;
import kh.com.bicbank.corp.user.shared.enumeration.domain.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBepClientRequestDto {

    private UUID customerId;

    private String customerName;

    private UUID userId;

    private String username;

    private UserType userType = UserType.BEP;

    private String idpIssuer;

    private String bfCode;

    private String bfName;

    private String remark;

    private List<UserBepTransactionDataDto> transactionData;

    private String callbackUrl;

    private CallBackMethod callbackMethod;

    private String bfgId;

    private String bfgName;

    private String bfgCode;

    private Object additionalData;

    private String fullName;

    private String branchCode;

    private String departmentCode;


    public ApprovalClientCreateDataRequestDto create() {

        return ApprovalClientCreateDataRequestDto.builder()
                .customerId(customerId)
                .customerName(customerName)
                .userId(userId)
                .username(username)
                .userType(userType)
                .idpIssuer(idpIssuer)
                .bfCode(bfCode)
                .bfName(bfName)
                .remark(remark)
                .transactionData(transactionData)
                .callBackUrl(callbackUrl)
                .callbackMethod(callbackMethod)
                .bfgId(bfgId)
                .bfgName(bfgName)
                .bfgCode(bfgCode)
                .additionalData(additionalData)
                .fullName(fullName)
                .branchCode(branchCode)
                .departmentCode(departmentCode)
                .build();
    }
}
