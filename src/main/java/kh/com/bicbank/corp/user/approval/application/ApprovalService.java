package kh.com.bicbank.corp.user.approval.application;

import com.bic.efw.core.payload.Response;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import kh.com.bicbank.corp.user.account.domain.Account;
import kh.com.bicbank.corp.user.approval.domain.ApprovalInformation;
import kh.com.bicbank.corp.user.approval.domain.ApprovalResponse;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import kh.com.bicbank.corp.user.corpuser.domain.bep.BepRepository;
import kh.com.bicbank.corp.user.corpuser.domain.callback.*;
import kh.com.bicbank.corp.user.corpuser.domain.user.InternalRestApprovalToCreate;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserToCreate;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserToUpdate;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestCorpUserToCreateRequest;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestResetPasswordRequest;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestUpdateUserStatusRequest;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestUserToCreate;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestUserToUpdate;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import kh.com.bicbank.corp.user.shared.authentication.domain.UserId;
import kh.com.bicbank.corp.user.shared.constant.domain.UserIdentityEndpointConstant.UserIdentityCallback;
import kh.com.bicbank.corp.user.shared.datetime.infrastructure.DatetimeUtils;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFCode;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFGCode;
import kh.com.bicbank.corp.user.shared.enumeration.domain.UserType;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakProperties;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import kh.com.bicbank.corp.user.wire.config.UserIdentityProperties;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Service
public class ApprovalService {
  private static final Logger log = LoggerFactory.getLogger(ApprovalService.class);
  private final UserService userService;
  private final DatetimeUtils datetimeUtils;
  private final RestHandler restHandler;
  private final ObjectMapper objectMapper;
  private final UserIdentityProperties userIdentityProperties;
  private final BepRepository bepRepository;
  private final String CORP_REALM;

  public ApprovalService(
      UserService userService,
      DatetimeUtils datetimeUtils,
      RestHandler restHandler,
      ObjectMapper objectMapper,
      UserIdentityProperties userIdentityProperties,
      KeycloakProperties keycloakProperties,
      BepRepository bepRepository) {
    this.userService = userService;
    this.datetimeUtils = datetimeUtils;
    this.restHandler = restHandler;
    this.objectMapper = objectMapper;
    this.userIdentityProperties = userIdentityProperties;
    CORP_REALM = keycloakProperties.getRealmCDB();
    this.bepRepository = bepRepository;
  }

  public ApprovalResponse postApprovalCreateUser(RestCorpUserToCreateRequest request) {

    Account account = Account.builder()
        .username(request.getUsername())
        .firstname(request.getFirstName())
        .lastname(request.getLastName())
        .email("")
        .roles(new ArrayList<>())
        .customerId("")
        .branchCode(request.getBranchCode())
        .departmentCode(request.getDepartmentCode())
        .isLocalUser(request.getLocalUser())
        .realmName("")
        .userId(new UserId(request.getUserId()))
        .build();
    return postApprovalCreateUser(account, request.getUserToCreate());
  }

  public ApprovalResponse postApprovalCreateUser(Account account, RestUserToCreate request) {

    UUID transactionId = UUID.randomUUID();
    UserToCreate userToCreate = UserToCreate.from(request);
    userService.validateInfoCreateUser(userToCreate);
    InternalCreateUserCallback internalCallback = InternalCreateUserCallback.builder()
            .userToCreate(userToCreate)
            .transactionId(transactionId)
            .build();

    // for local user, get branch & department from token
    // for azure user, get from db

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Create user] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .bfCode(BFCode.CREATE_USER.getCode())
            .bfName(BFCode.CREATE_USER.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.CREATE_USER))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);

    return ApprovalResponse.from(approvalInformation);
  }

  public ApprovalResponse postApprovalUpdateUser(
      Account account, String userId, RestUserToUpdate request) {
    UUID transactionId = UUID.randomUUID();
    UserToUpdate userToUpdate = UserToUpdate.from(userId, request);
    userService.validateInfoUpdateUser(userId, userToUpdate);
    InternalUpdateUserCallback internalCallback = InternalUpdateUserCallback.builder()
            .userToUpdate(userToUpdate)
            .transactionId(transactionId)
            .build();

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Update user] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .bfCode(BFCode.UPDATE_USER.getCode())
            .bfName(BFCode.UPDATE_USER.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(String.format(
                    ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.UPDATE_USER), userId))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);
    return ApprovalResponse.from(approvalInformation);
  }

  public ApprovalResponse postApprovalLockUser(
      Account account, String userId, RestUpdateUserStatusRequest request) {
    userService.validateInfoUpdateUserStatus(userId, request.getCustomerId());
    UUID transactionId = UUID.randomUUID();
    UpdateUserStatusCallback data =
        UpdateUserStatusCallback.builder()
            .userId(userId)
            .status(UserStatus.LOCKED.name())
            .reason(request.getReason())
            .build();
    InternalUpdateUserStatusCallback internalCallback = InternalUpdateUserStatusCallback.builder()
            .updateUserStatusCallback(data)
            .transactionId(transactionId)
            .build();

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Lock user] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .bfCode(BFCode.LOCK_USER.getCode())
            .bfName(BFCode.LOCK_USER.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(String.format(
                    ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.UPDATE_USER_STATUS), userId))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);
    return ApprovalResponse.from(approvalInformation);
  }

  public ApprovalResponse postApprovalUnlockUser(
      Account account, String userId, RestUpdateUserStatusRequest request) {
    userService.validateInfoUpdateUserStatus(userId, request.getCustomerId());
    UpdateUserStatusCallback data =
        UpdateUserStatusCallback.builder()
            .userId(userId)
            .status(UserStatus.ACTIVE.name())
            .reason(request.getReason())
            .build();
    UUID transactionId = UUID.randomUUID();
    InternalUpdateUserStatusCallback internalCallback = InternalUpdateUserStatusCallback.builder()
            .updateUserStatusCallback(data)
            .transactionId(transactionId)
            .build();

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Unlock user] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .bfCode(BFCode.UNLOCK_USER.getCode())
            .bfName(BFCode.UNLOCK_USER.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(String.format(
                    ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.UPDATE_USER_STATUS), userId))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);
    return ApprovalResponse.from(approvalInformation);
  }

  public ApprovalResponse postApprovalRemoveUser(
      Account account, String userId, RestUpdateUserStatusRequest request) {
    userService.validateInfoUpdateUserStatus(userId, request.getCustomerId());
    UpdateUserStatusCallback data =
        UpdateUserStatusCallback.builder()
            .userId(userId)
            .status(UserStatus.INACTIVE.name())
            .reason(request.getReason())
            .build();
    UUID transactionId = UUID.randomUUID();
    InternalUpdateUserStatusCallback internalCallback = InternalUpdateUserStatusCallback.builder()
            .updateUserStatusCallback(data)
            .transactionId(transactionId)
            .build();

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Remove user] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .bfCode(BFCode.REMOVE_USER.getCode())
            .bfName(BFCode.REMOVE_USER.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(String.format(
                    ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.UPDATE_USER_STATUS), userId))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);
    return ApprovalResponse.from(approvalInformation);
  }

  public ApprovalResponse postApprovalResetPassword(
      Account account, String userId, RestResetPasswordRequest request) {
    UUID transactionId = UUID.randomUUID();
    InternalResetPasswordCallback internalCallback = InternalResetPasswordCallback.builder()
            .resetPasswordCallback(ResetPasswordCallback.from(userId, request))
            .transactionId(transactionId)
            .build();

    Map<String, String> branchAndDepartment = getBranchAndDepartmentByAccount(account);

    Map<String, Object> templateContent = new HashMap<>();
    templateContent.put("NAME", request.getCustomerName());

    if (StringUtils.isEmpty(userIdentityProperties.getEndpoint())) {
      log.error("[Corp - Reset password] Property base callback url not found!");
    }

    InternalRestApprovalToCreate approvalToCreate =
        InternalRestApprovalToCreate.builder()
            .customerId(request.getCustomerId())
            .customerName(request.getCustomerName())
            .userType(UserType.BEP.toString())
            .idpIssuer(UserType.BEP.toString())
            .userId(account.userId().userId())
            .username(account.username().username())
            .fullName(account.name().get())
            .branchCode(branchAndDepartment.get("branchCode"))
            .departmentCode(branchAndDepartment.get("departmentCode"))
            .templateContent(templateContent)
            .remark(request.getRemark())
            .templateContent(templateContent)
            .bfCode(BFCode.RESET_PASSWORD.getCode())
            .bfName(BFCode.RESET_PASSWORD.getName())
            .bfgCode(BFGCode.USER_MANAGEMENT.getCode())
            .bfgName(BFGCode.USER_MANAGEMENT.getName())
            .transactionData(internalCallback)
            .additionalData(request.getAdditionalData())
            .callBackUrl(String.format(
                    ObjectUtils.isEmpty(userIdentityProperties.getEndpoint()) ? "" : userIdentityProperties.getEndpoint()
                    .concat(UserIdentityCallback.RESET_PASSWORD), userId))
            .build();
    Response postApprovalRes = restHandler.postApproval(approvalToCreate);
    ApprovalInformation approvalInformation =
        objectMapper.convertValue(postApprovalRes.getData(), new TypeReference<>() {});
    userService.saveTransaction(approvalInformation.getTransactionData(), transactionId);
    return ApprovalResponse.from(approvalInformation);
  }

  /**
   * @param account
   * @return
   */
  private Map<String, String> getBranchAndDepartmentByAccount(Account account) {
    String branchCode = null;
    String departmentCode = null;
    if (account.isLocalUser().isLocalUser()) {
      branchCode = account.branchCode().branchCode();
      departmentCode = account.departmentCode().departmentCode();
    } else {
      UserBepEntity userBepEntity = bepRepository.findById(account.userId().userId());
      if (Objects.nonNull(userBepEntity)) {
        branchCode = userBepEntity.getBranchCode();
        departmentCode = userBepEntity.getDepartmentCode();
      }
    }
    Map<String, String> brandAndDepartment = new HashMap<>() {};
    brandAndDepartment.put("branchCode", branchCode);
    brandAndDepartment.put("departmentCode", departmentCode);
    return brandAndDepartment;
  }
}
