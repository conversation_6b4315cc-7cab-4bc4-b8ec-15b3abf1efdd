package kh.com.bicbank.corp.user.account.domain;

import com.bic.efw.core.error.Assert;
import kh.com.bicbank.corp.user.shared.authentication.domain.*;
import kh.com.bicbank.corp.user.shared.useridentity.domain.Email;
import kh.com.bicbank.corp.user.shared.useridentity.domain.Firstname;
import kh.com.bicbank.corp.user.shared.useridentity.domain.Lastname;
import kh.com.bicbank.corp.user.shared.useridentity.domain.Name;

import java.util.Collection;
import java.util.stream.Collectors;

public class Account {

  private final Username username;
  private final Name name;
  private final Email email;
  private final Roles roles;
  private final CustomerId customerId;
  private final UserId userId;
  private final BranchCode branchCode;
  private final DepartmentCode departmentCode;
  private final IsLocalUser isLocalUser;
  private final RealmName realmName;

  private Account(UserBuilder builder) {
    username = builder.username;
    name = new Name(builder.firstname, builder.lastname);
    email = builder.email;
    roles = builder.roles;
    customerId = builder.customerId;
    userId = builder.userId;
    branchCode = builder.branchCode;
    departmentCode = builder.departmentCode;
    isLocalUser = builder.isLocalUser;
    realmName = builder.realmName;
  }

  public static AccountUsernameBuilder builder() {
    return new UserBuilder();
  }

  public UserId userId() {
    return userId;
  }

  public Username username() {
    return username;
  }

  public Name name() {
    return name;
  }

  public Email email() {
    return email;
  }

  public Roles roles() {
    return roles;
  }

  public CustomerId customerId() {
    return customerId;
  }

  public BranchCode branchCode() {
    return branchCode;
  }

  public DepartmentCode departmentCode() {
    return departmentCode;
  }

  public IsLocalUser isLocalUser() {
    return isLocalUser;
  }

  public RealmName realmName() {
    return realmName;
  }

  public static class UserBuilder
      implements
      AccountIdBuilder,
      AccountUsernameBuilder,
      AccountFirstnameBuilder,
      AccountLastnameBuilder,
      AccountEmailBuilder,
      AccountRolesBuilder,
      AccountCustomerIdBuilder,
      AccountBranchCodeBuilder,
      AccountDepartmentCodeBuilder,
      AccountIsLocalUserBuilder,
      AccountRealmNameBuilder,
      AccountOptionalFieldBuilder {

    private Username username;
    private Firstname firstname;
    private Lastname lastname;
    private Email email;
    private Roles roles;
    private CustomerId customerId;
    private UserId userId;

    private BranchCode branchCode;
    private DepartmentCode departmentCode;

    private IsLocalUser isLocalUser;

    private RealmName realmName;

    private UserBuilder() {}

    @Override
    public AccountOptionalFieldBuilder userId(UserId userId) {
      this.userId = userId;
      return this;
    }

    @Override
    public AccountFirstnameBuilder username(Username username) {
      this.username = username;
      return this;
    }

    @Override
    public AccountLastnameBuilder firstname(Firstname firstname) {
      this.firstname = firstname;
      return this;
    }

    @Override
    public AccountEmailBuilder lastname(Lastname lastname) {
      this.lastname = lastname;
      return this;
    }

    @Override
    public AccountRolesBuilder email(Email email) {
      this.email = email;
      return this;
    }

    @Override
    public Account build() {
      return new Account(this);
    }
    @Override
    public AccountCustomerIdBuilder roles(Roles roles) {
      this.roles = roles;
      return this;
    }

    @Override
    public AccountBranchCodeBuilder customerId(CustomerId customerId) {
      this.customerId = customerId;
      return this;
    }

    @Override
    public AccountDepartmentCodeBuilder branchCode(BranchCode branchCode) {
      this.branchCode = branchCode;
      return this;
    }

    @Override
    public AccountIsLocalUserBuilder departmentCode(DepartmentCode departmentCode) {
      this.departmentCode = departmentCode;
      return this;
    }

    @Override
    public AccountRealmNameBuilder isLocalUser(IsLocalUser isLocalUser) {
      this.isLocalUser = isLocalUser;
      return this;
    }

    @Override
    public AccountOptionalFieldBuilder realmName(RealmName realmName) {
      this.realmName = realmName;
      return this;
    }
  }

  public interface AccountIdBuilder {
    AccountOptionalFieldBuilder userId(UserId userId);

    default AccountOptionalFieldBuilder userId(String userId) {
      return userId(new UserId(userId));
    }
  }

  public interface AccountUsernameBuilder {
    AccountFirstnameBuilder username(Username username);

    default AccountFirstnameBuilder username(String username) {
      return username(new Username(username));
    }
  }

  public interface AccountFirstnameBuilder {
    AccountLastnameBuilder firstname(Firstname firstname);

    default AccountLastnameBuilder firstname(String firstname) {
      return firstname(new Firstname(firstname));
    }
  }

  public interface AccountLastnameBuilder {
    AccountEmailBuilder lastname(Lastname lastname);

    default AccountEmailBuilder lastname(String lastname) {
      return lastname(new Lastname(lastname));
    }
  }

  public interface AccountEmailBuilder {
    AccountRolesBuilder email(Email email);

    default AccountRolesBuilder email(String email) {
      return email(new Email(email));
    }
  }

  public interface AccountRolesBuilder {
    AccountCustomerIdBuilder roles(Roles roles);

    default AccountCustomerIdBuilder roles(Collection<String> roles) {
      Assert.notNull("roles", roles);

      return roles(new Roles(roles.stream().map(Role::from).collect(Collectors.toUnmodifiableSet())));
    }
  }

  public interface AccountCustomerIdBuilder {
    AccountBranchCodeBuilder customerId(CustomerId customerId);

    default AccountBranchCodeBuilder customerId(String customerId) {
      return customerId(new CustomerId(customerId));
    }
  }

  public interface AccountBranchCodeBuilder {
    AccountDepartmentCodeBuilder branchCode(BranchCode branchCode);

    default AccountDepartmentCodeBuilder branchCode(String branchCode) {
      return branchCode(new BranchCode(branchCode));
    }
  }

  public interface AccountDepartmentCodeBuilder {
    AccountIsLocalUserBuilder departmentCode(DepartmentCode departmentCode);

    default AccountIsLocalUserBuilder departmentCode(String departmentCode) {
      return departmentCode(new DepartmentCode(departmentCode));
    }
  }

  public interface AccountIsLocalUserBuilder {
    AccountRealmNameBuilder isLocalUser(IsLocalUser isLocalUser);

    default AccountRealmNameBuilder isLocalUser(boolean isLocalUser) {
      return isLocalUser(new IsLocalUser(isLocalUser));
    }
  }

  public interface AccountRealmNameBuilder {
    AccountOptionalFieldBuilder realmName(RealmName realmName);

    default AccountOptionalFieldBuilder realmName(String realmName) {
      return realmName(new RealmName(realmName));
    }
  }

  public interface AccountOptionalFieldBuilder {
    Account build();

    AccountOptionalFieldBuilder userId(UserId sub);
  }
}