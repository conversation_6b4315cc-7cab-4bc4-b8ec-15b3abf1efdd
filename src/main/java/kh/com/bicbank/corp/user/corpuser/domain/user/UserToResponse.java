package kh.com.bicbank.corp.user.corpuser.domain.user;

import kh.com.bicbank.corp.user.shared.datetime.infrastructure.DatetimeUtils;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakAttributesEnum;
import kh.com.bicbank.corp.user.shared.rest.domain.User;
import kh.com.bicbank.corp.user.shared.rest.domain.UserAttribute;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.keycloak.representations.idm.UserRepresentation;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static kh.com.bicbank.corp.user.shared.constant.domain.PatternConstant.DateTime.BIC_DATE_TIME_PATTERN;

@Setter
@Getter
@Builder
public class UserToResponse {
  private String userId;
  private String customerId;
  private String userName;
  private String firstName;
  private String lastName;
  private String firstNameKhr;
  private String lastNameKhr;
  private String identifyType;
  private String idNumber;
  private String dob;
  private String gender;
  private String phoneNumber;
  private String address;
  private String email;
  private String jobTitle;
  private String jobRoleId;
  private String description;
  private String issuedDate;
  private String expiredDate;
  private String createdAt;
  private String lastLogin;
  private String status;
  private String gbshortName;

  public static UserToResponse from(UserRepresentation user) {
    String userId = user.getId();
    String userName = user.getUsername();
    String firstName = user.getFirstName();
    String lastName = user.getLastName();
    String email = user.getEmail();
    String createdAt = new DatetimeUtils().convertToUTCDate(user.getCreatedTimestamp());
    String lastLogin = null;
    String customerId = null;
    String phoneNumber = null;
    String address = null;
    String firstNameKhr = null;
    String lastNameKhr = null;
    String identifyType = null;
    String idNumber = null;
    String dob = null;
    String gender = null;
    String jobTitle = null;
    String jobRoleId = null;
    String description = null;
    String issuedDate = null;
    String expiredDate = null;
    String status = user.isEnabled() ? UserStatus.ACTIVE.name() : UserStatus.LOCKED.name();

    Map<String, List<String>> attributes = user.getAttributes();
    if (attributes != null && !attributes.isEmpty()) {
      for (Map.Entry<String, List<String>> entry : attributes.entrySet()) {
        switch (entry.getKey()) {
          case KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE:
            customerId = entry.getValue().get(0);
            break;

          case KeycloakAttributesEnum.MOBILE_ATTRIBUTE:
            phoneNumber = entry.getValue().get(0);
            break;

          case KeycloakAttributesEnum.ADDRESS_ATTRIBUTE:
            address = entry.getValue().get(0);
            break;

          case KeycloakAttributesEnum.FIRST_NAME_KHR_ATTRIBUTE:
            firstNameKhr = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.LAST_NAME_KHR_ATTRIBUTE:
            lastNameKhr = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.IDENTIFY_TYPE_ATTRIBUTE:
            identifyType = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.ID_NUMBER_ATTRIBUTE:
            idNumber = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.DOB_ATTRIBUTE:
            dob = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.GENDER_ATTRIBUTE:
            gender = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.JOB_TITLE_ATTRIBUTE:
            jobTitle = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.JOB_ROLE_ID_ATTRIBUTE:
            jobRoleId = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.DESCRIPTION_ATTRIBUTE:
            description = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.ISSUED_DATE_ATTRIBUTE:
            issuedDate = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.EXPIRY_DATE_ATTRIBUTE:
            expiredDate = entry.getValue().get(0);
            break;
          case KeycloakAttributesEnum.LAST_LOGIN_ATTRIBUTE:
            lastLogin = parseLastLoginTime(entry.getValue().get(0));
            break;
          default:
            break;
        }
      }
    }

    return UserToResponse.builder()
        .userId(userId)
        .customerId(customerId)
        .userName(userName)
        .email(email)
        .firstName(firstName)
        .lastName(lastName)
        .firstNameKhr(firstNameKhr)
        .lastNameKhr(lastNameKhr)
        .createdAt(createdAt)
        .identifyType(identifyType)
        .idNumber(idNumber)
        .dob(dob)
        .gender(gender)
        .jobTitle(jobTitle)
        .jobRoleId(jobRoleId)
        .phoneNumber(phoneNumber)
        .address(address)
        .description(description)
        .issuedDate(issuedDate)
        .expiredDate(expiredDate)
        .lastLogin(lastLogin)
        .status(status)
        .build();
  }

  public static UserToResponse from(User user) {
    String userId = user.getId();
    String userName = user.getUsername();
    String firstName = user.getFirstName();
    String lastName = user.getLastName();
    String email = user.getEmail();
    String createdAt = new DatetimeUtils().convertToUTCDate(user.getCreatedTimestamp());
    String lastLogin = null;
    String customerId = null;
    String phoneNumber = null;
    String address = null;
    String firstNameKhr = null;
    String lastNameKhr = null;
    String identifyType = null;
    String idNumber = null;
    String dob = null;
    String gender = null;
    String jobTitle = null;
    String jobRoleId = null;
    String description = null;
    String issuedDate = null;
    String expiredDate = null;
    String status = user.getEnabled() ? UserStatus.ACTIVE.name() : UserStatus.LOCKED.name();

    List<UserAttribute> attributes = user.getAttributes();
    if (!attributes.isEmpty()) {
      for (UserAttribute attribute : attributes) {
        switch (attribute.getName()) {
          case KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE:
            customerId = attribute.getValue();
            break;

          case KeycloakAttributesEnum.MOBILE_ATTRIBUTE:
            phoneNumber = attribute.getValue();
            break;

          case KeycloakAttributesEnum.ADDRESS_ATTRIBUTE:
            address = attribute.getValue();
            break;

          case KeycloakAttributesEnum.FIRST_NAME_KHR_ATTRIBUTE:
            firstNameKhr = attribute.getValue();
            break;
          case KeycloakAttributesEnum.LAST_NAME_KHR_ATTRIBUTE:
            lastNameKhr = attribute.getValue();
            break;
          case KeycloakAttributesEnum.IDENTIFY_TYPE_ATTRIBUTE:
            identifyType = attribute.getValue();
            break;
          case KeycloakAttributesEnum.ID_NUMBER_ATTRIBUTE:
            idNumber = attribute.getValue();
            break;
          case KeycloakAttributesEnum.DOB_ATTRIBUTE:
            dob = attribute.getValue();
            break;
          case KeycloakAttributesEnum.GENDER_ATTRIBUTE:
            gender = attribute.getValue();
            break;
          case KeycloakAttributesEnum.JOB_TITLE_ATTRIBUTE:
            jobTitle = attribute.getValue();
            break;
          case KeycloakAttributesEnum.JOB_ROLE_ID_ATTRIBUTE:
            jobRoleId = attribute.getValue();
            break;
          case KeycloakAttributesEnum.DESCRIPTION_ATTRIBUTE:
            description = attribute.getValue();
            break;
          case KeycloakAttributesEnum.ISSUED_DATE_ATTRIBUTE:
            issuedDate = attribute.getValue();
            break;
          case KeycloakAttributesEnum.EXPIRY_DATE_ATTRIBUTE:
            expiredDate = attribute.getValue();
            break;
          case KeycloakAttributesEnum.LAST_LOGIN_ATTRIBUTE:
            lastLogin = parseLastLoginTime(attribute.getValue());
            break;

          default:
            break;
        }
      }
    }

    return UserToResponse.builder()
        .userId(userId)
        .customerId(customerId)
        .userName(userName)
        .email(email)
        .firstName(firstName)
        .lastName(lastName)
        .firstNameKhr(firstNameKhr)
        .lastNameKhr(lastNameKhr)
        .createdAt(createdAt)
        .identifyType(identifyType)
        .idNumber(idNumber)
        .dob(dob)
        .gender(gender)
        .jobTitle(jobTitle)
        .jobRoleId(jobRoleId)
        .phoneNumber(phoneNumber)
        .address(address)
        .description(description)
        .issuedDate(issuedDate)
        .expiredDate(expiredDate)
        .lastLogin(lastLogin)
        .status(status)
        .build();
  }

  private static String parseLastLoginTime(String input) {
    int time = Integer.parseInt(input);
    Date date = new Date(time * 1000L);
    SimpleDateFormat formatter = new SimpleDateFormat(BIC_DATE_TIME_PATTERN);
    return formatter.format(date);
  }
}
