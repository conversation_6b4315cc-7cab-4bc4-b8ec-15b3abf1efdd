package kh.com.bicbank.corp.user.corpuser.domain.client.approval;


import com.fasterxml.jackson.annotation.JsonProperty;
import kh.com.bicbank.corp.user.shared.enumeration.domain.ApprovalStatus;
import kh.com.bicbank.corp.user.shared.enumeration.domain.UserType;
import lombok.*;

import java.util.List;
import java.util.UUID;

@Builder
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalClientCreateDataResponseDto {

    @JsonProperty("id")
    private UUID id;

    @JsonProperty("userId")
    private UUID userId;

    @JsonProperty("userType")
    private UserType userType;

    @JsonProperty("idpIssuer")
    private String idpIssuer;

    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("bfCode")
    private String bfCode;

    @JsonProperty("bfName")
    private String bfName;

    @JsonProperty("transactionData")
    private Object transactionData;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("status")
    private ApprovalStatus status;

    @JsonProperty("createdDate")
    private String createdDate;

    @JsonProperty("processDate")
    private String processDate;

    @JsonProperty("submittedBy")
    private UUID submittedBy;

    @JsonProperty("submittedByName")
    private String submittedByName;

    @JsonProperty("createdBy")
    private UUID createdBy;

    @JsonProperty("createdByName")
    private String createdByName;

    @JsonProperty("approvalPolicyId")
    private UUID approvalPolicyId;

    @JsonProperty("approvalPolicyName")
    private String approvalPolicyName;

    @JsonProperty("lastApprovedBy")
    private UUID lastApprovedBy;

    @JsonProperty("lastApprovedByName")
    private String lastApprovedByName;

    @JsonProperty("confirmationId")
    private UUID confirmationId;

    @JsonProperty("approvalRecords")
    private List<Object> approvalRecords;

    @JsonProperty("callBackResponse")
    private Object callBackResponse;

    @JsonProperty("submittedDate")
    private String submittedDate;

    @JsonProperty("isLastApproval")
    private Boolean isLastApproval;

}
