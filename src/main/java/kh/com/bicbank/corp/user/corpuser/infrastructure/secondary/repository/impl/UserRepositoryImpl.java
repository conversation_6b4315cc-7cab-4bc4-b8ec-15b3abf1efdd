package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.impl;

import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserEntity;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.JpaUserRepository;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Repository
public class UserRepositoryImpl implements UserRepository {

  private final JpaUserRepository repository;

  public UserRepositoryImpl(JpaUserRepository repository) {
    this.repository = repository;
  }

  @Override
  public UserEntity save(UserEntity entity) {
    return repository.save(entity);
  }

  @Override
  public void updateStatusByIdpUserId(String idpUserId, UserStatus status) {
    log.info("updateStatusByIdpUserId idpUserId: {} - status: {}", idpUserId, status);
    List<UserEntity> userEntities = repository.findByIdpUserId(idpUserId);
    if (!userEntities.isEmpty()) {
      UserEntity userEntity = userEntities.get(0);
      userEntity.setStatus(status);
      userEntity.setLastModifiedDate(LocalDateTime.now());
      repository.saveAndFlush(userEntity);
    }
  }

  @Override
  public List<UserEntity> findByIdpUserId(String id) {
    return repository.findByIdpUserId(id);
  }

  @Override
  public UserEntity findById(String id) {
    return repository.findById(id);
  }

}
