package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity;


import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;


@MappedSuperclass
public class AuditEntity implements Serializable {

    @Temporal(TemporalType.TIMESTAMP)
    @CreatedDate
    @Column(name = "create_date")
    private Date createdDate;

    @CreatedBy
    @Column(name = "create_by")
    private String createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @LastModifiedDate
    @Column(name = "last_update_date")
    private Date lastUpdateDate;

    @LastModifiedBy
    @Column(name = "last_update_by")
    private String lastUpdateBy;

}
