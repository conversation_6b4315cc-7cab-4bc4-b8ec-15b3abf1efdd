package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.bep;

import com.bic.efw.core.payload.Response;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kh.com.bicbank.corp.user.corpuser.application.BepUserService;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepTransactionDataDto;
import kh.com.bicbank.corp.user.corpuser.domain.callback.RequestStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
@Slf4j
@RestController
@Tag(name = "BEP User Management ")
@Hidden
@RequestMapping("/internal/api/bep/users/management")
@RequiredArgsConstructor
public class BepUserManagementInternalResource {
    private final BepUserService bepUserService;
    @PostMapping("/callback/enroll-user")
    @Operation(summary = "Callback Approve/Reject enroll user bep")
    @ApiResponse(description = "This endpoint enroll user bep." + "\n", responseCode = "200")
    ResponseEntity enrollUser(@Validated @RequestBody RestInternalUserBepRequest enrollRequest) {
        log.info("status callback enroll user " +enrollRequest.getStatus());
        if (enrollRequest.getStatus().equals(RequestStatus.REJECTED)) {
            Response response = new Response(true).setData(enrollRequest.getTransactionData());
            return new ResponseEntity<>(response, HttpStatus.OK);
        }
        List<UserBepTransactionDataDto> transactionData = enrollRequest.getTransactionData();

        if (CollectionUtils.isEmpty(transactionData)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "TransactionData is not empty.");
        }
        log.info("assign job roles");
        bepUserService.assignJobRoles(transactionData);
        log.info("update user bep enroll or update");
        bepUserService.updateUsersBepEnroll(transactionData);

        Response response = new Response(true).setData(transactionData);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PostMapping("/callback/suspend-user")
    @Operation(summary = "Callback Approve/Reject suspend user bep")
    @ApiResponse(description = "This endpoint suspend user bep." + "\n", responseCode = "200")
    ResponseEntity suspendUser(@Validated @RequestBody RestInternalUserBepRequest suspendRequest) {
        if (suspendRequest.getStatus().equals(RequestStatus.REJECTED)) {
            Response response = new Response(true).setData(suspendRequest.getTransactionData());
            return new ResponseEntity<>(response, HttpStatus.OK);
        }
        List<UserBepTransactionDataDto> transactionData = suspendRequest.getTransactionData();

        if (CollectionUtils.isEmpty(transactionData)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "TransactionData is not empty.");
        }

        bepUserService.updateUsersBepSuspend(transactionData);
        Response response = new Response(true).setData(transactionData);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PostMapping("/callback/active-user")
    @Operation(summary = "Callback Approve/Reject active user bep")
    @ApiResponse(description = "This endpoint active user bep." + "\n", responseCode = "200")
    ResponseEntity activeUser(@Validated @RequestBody RestInternalUserBepRequest suspendRequest) {
        if (suspendRequest.getStatus().equals(RequestStatus.REJECTED)) {
            Response response = new Response(true).setData(suspendRequest.getTransactionData());
            return new ResponseEntity<>(response, HttpStatus.OK);
        }
        List<UserBepTransactionDataDto> transactionData = suspendRequest.getTransactionData();

        if (CollectionUtils.isEmpty(transactionData)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "TransactionData is not empty.");
        }

        bepUserService.activeUsersBep(transactionData);
        Response response = new Response(true).setData(transactionData);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUserBep.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/same-branch-department/{userId}")
    ResponseEntity<Response> getUserWithSameBranchAndDepartment(@PathVariable String userId) {
        if (StringUtils.isEmpty(userId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Users is not empty.");
        }
        return ResponseEntity.ok(bepUserService.getUsersSameBranchAnhDepartment(userId));
    }

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUserBep.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/{userId}")
    ResponseEntity<Response> getUserDetail(@PathVariable String userId) {
        return ResponseEntity.ok(bepUserService.getUserById(userId));
    }

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUserBep.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/get-emails-with/{userIds}")
    ResponseEntity<Response> getEmailsWithUserIds(@PathVariable String userIds) {
        List<String> listUserId = new ArrayList<String>(Arrays.asList(userIds.split(",")));
        return ResponseEntity.ok(bepUserService.getInfoUserFromListId(listUserId));
    }

    @ApiResponses({
            @ApiResponse(responseCode = "200"
                    , content = { @Content(schema = @Schema(implementation = RestUserBep.class)
                    , mediaType = "application/json") }),
            @ApiResponse(responseCode = "404"
                    , content = { @Content(schema = @Schema(implementation = Error.class)
                    , mediaType = "application/json") })
    })
    @GetMapping("/get-emails-via-Keycloak-with/{userIds}")
    ResponseEntity<Response> getEmailsWithUserIdsViaKeycloak(@PathVariable String userIds) {
        return ResponseEntity.ok(bepUserService.getInfoUserFromListIdViaKeycloak(userIds));
    }

}
