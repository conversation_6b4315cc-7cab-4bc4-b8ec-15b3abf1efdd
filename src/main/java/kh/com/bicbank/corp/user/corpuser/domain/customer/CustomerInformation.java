package kh.com.bicbank.corp.user.corpuser.domain.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Setter
@Getter
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerInformation {
  @JsonProperty("customerId")
  private final String customerId;

  @JsonProperty("businessName")
  private final String businessName;

  @JsonProperty("businessNameKhmer")
  private final String businessNameKhmer;

  @JsonProperty("businessPhoneNumber")
  private final String businessPhoneNumber;

  @JsonProperty("emailAddress")
  private final String emailAddress;

  @JsonProperty("customerMnemonic")
  private final String customerMnemonic;

  @JsonProperty("companyTin")
  private final String companyTin;
  @JsonProperty("customerCategory")
  private final String customerCategory;
  @JsonProperty("customerType")
  private final String customerType;
  @JsonProperty("customerContactSource")
  private final String customerContactSource;
  @JsonProperty("segmentType")
  private final String segmentType;
  @JsonProperty("businessRegistrationNumber")
  private final String businessRegistrationNumber;
  @JsonProperty("servicePackage")
  private final String servicePackage;
  @JsonProperty("issueBankBranch")
  private final String issueBankBranch;
  @JsonProperty("headOfficeAddress")
  private final String headOfficeAddress;
  @JsonProperty("businessAddress")
  private final String businessAddress;
  @JsonProperty("status")
  private final String status;
  @JsonProperty("sector")
  private String sector;
  @JsonProperty("registeredCountry")
  private String registeredCountry;
  @JsonProperty("mainActivity")
  private String mainActivity;
  @JsonProperty("businessDescription")
  private String businessDescription;
  @JsonProperty("numberOfEmployee")
  private Integer numberOfEmployee;
  @JsonProperty("kycStatus")
  private String kycStatus;
  @JsonProperty("reviewStatus")
  private String reviewStatus;
  @JsonProperty("referral")
  private String referral;
  @JsonProperty("residence")
  private String residence;
  @JsonProperty("creditRiskRating")
  private String creditRiskRating;
  @JsonProperty("gbshortName")
  private String gbshortName;
}
