package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.bep;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepResponse;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Schema(name = "userBep", description = "user bep")
@Builder
@Getter
@Setter
@JsonInclude
public class RestUserBep {
    @JsonProperty("userId")
    private String userId;

    @JsonProperty("userName")
    private String userName;

    @JsonProperty("firstName")
    private String firstName;

    @JsonProperty("lastName")
    private String lastName;

    @JsonProperty("firstNameKhr")
    private String firstNameKhr;

    @JsonProperty("lastNameKhr")
    private String lastNameKhr;

    @JsonProperty("identifyType")
    private String identifyType;

    @JsonProperty("idNumber")
    private String idNumber;

    @JsonProperty("dob")
    private String dob;

    @JsonProperty("gender")
    private String gender;

    @JsonProperty("phoneNumber")
    private String phoneNumber;

    @JsonProperty("address")
    private String address;

    @JsonProperty("email")
    private String email;

    @JsonProperty("jobTitle")
    private String jobTitle;

    @JsonProperty("jobRoleId")
    private String jobRoleId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("issuedDate")
    private String issuedDate;

    @JsonProperty("expiredDate")
    private String expiredDate;

    @JsonProperty("createdAt")
    private  String createdAt;

    @JsonProperty("status")
    private  String status;

    @JsonProperty("lastLogin")
    private  String lastLogin;


    @JsonProperty("department")
    private  String department;

    @JsonProperty("employeeId")
    private String employeeId;

    @JsonProperty("branch")
    private String branch;

    @JsonProperty("branchCode")
    private String branchCode;

    @JsonProperty("departmentCode")
    private String departmentCode;

    @JsonProperty("jobRoleIds")
    private String jobRoleIds;





    public static RestUserBep from(UserBepResponse user) {

        return RestUserBep.builder()
                .userId(user.getUserId())
                .userName(user.getUserName())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .firstNameKhr(user.getFirstNameKhr())
                .lastNameKhr(user.getLastNameKhr())
                .identifyType(user.getIdentifyType())
                .idNumber(user.getIdNumber())
                .dob(user.getDob())
                .gender(user.getGender())
                .phoneNumber(user.getPhoneNumber())
                .address(user.getAddress())
                .email(user.getEmail())
                .jobTitle(user.getJobTitle())
                .jobRoleId(user.getJobRoleId())
                .description(user.getDescription())
                .issuedDate(user.getIssuedDate())
                .expiredDate(user.getExpiredDate())
                .createdAt(user.getCreatedAt())
                .status(user.getStatus())
                .lastLogin(user.getLastLogin())
                .department(user.getDepartment())
                .employeeId(user.getEmployeeId())
                .branch(user.getBranch())
                .branchCode(user.getBranchCode())
                .departmentCode(user.getDepartmentCode())
                .jobRoleIds(user.getJobRoleIds())
                .build();
    }
}
