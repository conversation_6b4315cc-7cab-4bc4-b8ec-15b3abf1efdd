package kh.com.bicbank.corp.user.access_control.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BusinessProcessSetting {
    private UUID id;
    private String bfCode;
    private boolean makerSigning;
    private boolean checkerSigning;
    private boolean isApproval;
    private boolean isRelatedPayment;
    private boolean isKeepHistory;
    private Boolean allowManyRequest;
    private String customerId;
    private String bfType;
    private AccessControlStatus status;
}
