package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity;

import jakarta.persistence.*;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepStatus;
import lombok.*;

@Getter
@Setter
@Entity
@Table(name = "bep_user")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBepEntity {
    @Id
    private String id;
    private String displayName;
    private String givenName;
    private String jobTitle;
    private String mail;
    private String mobilePhone;
    private String officeLocation;
    private String preferredLanguage;
    private String surname;
    private String userPrincipalName;
    private String department;// from azure
    private Boolean accountEnabled;
    private String createdDateTime;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "status")
    private UserBepStatus status;

    private String employeeId;

    private String branch;// from azure
    private String branchCode;
    private String departmentCode;
    private String jobRoleIds;


}
