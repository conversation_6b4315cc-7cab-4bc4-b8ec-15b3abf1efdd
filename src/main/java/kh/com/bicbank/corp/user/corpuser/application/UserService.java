package kh.com.bicbank.corp.user.corpuser.application;

import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import kh.com.bicbank.corp.user.corpuser.domain.callback.ChangePasswordCallback;
import kh.com.bicbank.corp.user.corpuser.domain.callback.UpdateUserStatusCallback;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserToCreate;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserToUpdate;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestIsExist;
import org.springframework.validation.BindingResult;

import java.util.UUID;

public interface UserService {
  Response createUserWithValidator(UserToCreate request);

  Response getUserList(Integer pageNo, Integer pageSize);

  Response getCorpUserDetail(String userId);

  Response getCorpUserInfo(String userId);

  Response getUserInfoDetail(String userId);

  Response updateUser(String userId, UserToUpdate request);

  Response updateUserStatus(String userId, UpdateUserStatusCallback status);

  Response getUserListByAttribute(String customerId, RestCustomPageable customPageable);

  Response validateInfoCreateUser(UserToCreate request);

  Response validateInfoUpdateUser(String userId, UserToUpdate request);

  void validateInfoUpdateUserStatus(String userId, String customerId);

  Response getBepUserDetail(String userId);

  void validateUserInfo(BindingResult bindingResult);

  Response getUserList(
      String customerId,
      String search,
      String userName,
      String email,
      String idNumber,
      String phoneNumber,
      String status,
      RestCustomPageable customPageable);

  Response isExist(RestIsExist request);

  Response changePassword(String userId, ChangePasswordCallback request);

  Response getPasswordPolicy();

  Response queryUser(String customerId, String privilegeGroup, boolean isCDBUser);

  Response getUserListOfCustomerId(
          String customerId,
          String search,
          String userName,
          String email,
          String idNumber,
          String phoneNumber,
          String status);

  void saveTransaction(Object requestData, UUID id);
}
