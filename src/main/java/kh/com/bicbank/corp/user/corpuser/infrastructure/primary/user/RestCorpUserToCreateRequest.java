package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RestCorpUserToCreateRequest{

  @Valid
  RestUserToCreate userToCreate;
  @NotEmpty
  private String userId;
  @NotEmpty
  private String username;
  @NotEmpty
  private String firstName;
  @NotEmpty
  private String lastName;
  @NotEmpty
  private String branchCode;
  @NotEmpty
  private String departmentCode;
  @NotNull
  private Boolean localUser;
}
