package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.client.access_control;

import com.bic.efw.core.payload.Response;
import com.bic.efw.core.rest.RestTemplateCommon;
import com.fasterxml.jackson.databind.ObjectMapper;
import kh.com.bicbank.corp.user.corpuser.domain.access_control.AssignJobRoleResponse;
import kh.com.bicbank.corp.user.corpuser.domain.client.access.AccessControlClient;
import kh.com.bicbank.corp.user.corpuser.domain.client.access.UserJobRolesListRequest;
import kh.com.bicbank.corp.user.wire.config.AccessControlEndpointConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccessControlClientRest implements AccessControlClient {
    private final RestTemplateCommon restTemplateCommon;
    private final AccessControlEndpointConfig accessControlEndpointConfig;
    private final ObjectMapper objectMapper;
    @Override
    public boolean assignRoles(UserJobRolesListRequest userJobRolesListDto) {
        String url = accessControlEndpointConfig.getBaseUrl()
                .concat(accessControlEndpointConfig.getAssignJobRolesUsers());
        ParameterizedTypeReference<Response> typeReference =
                new ParameterizedTypeReference<>() {};
        log.info("begin call assign roles");
        if(CollectionUtils.isNotEmpty(userJobRolesListDto.getUserJobRolesList())) {
            log.info("userJobRolesListDto is not null ");
            log.info("size list request "+userJobRolesListDto.getUserJobRolesList().size());
            HttpEntity<UserJobRolesListRequest> requestEntity = new HttpEntity<>(userJobRolesListDto, null);
            ResponseEntity<Response> response = restTemplateCommon.exchange(url, HttpMethod.POST, requestEntity, typeReference);
            log.info(String.format("assignJobRole response: %s", response.getBody()));
            Response responseBody =
                    objectMapper.convertValue(response.getBody(), Response.class);
            AssignJobRoleResponse assignJobRoleResponse = objectMapper.convertValue(responseBody.getData(), AssignJobRoleResponse.class);
            if (assignJobRoleResponse.getHttpStatus() == 200) {
                return true;
            }
        }
        log.info("userJobRolesListDto is null  ");
        return false;
    }
}
