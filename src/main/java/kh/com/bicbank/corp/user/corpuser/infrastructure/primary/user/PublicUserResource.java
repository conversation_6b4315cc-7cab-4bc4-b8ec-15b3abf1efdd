package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import com.bic.efw.core.exception.ForbiddenException;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kh.com.bicbank.corp.user.access_control.application.AccessControlApplicationService;
import kh.com.bicbank.corp.user.account.application.AccountsApplicationService;
import kh.com.bicbank.corp.user.account.domain.Account;
import kh.com.bicbank.corp.user.approval.application.ApprovalService;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import kh.com.bicbank.corp.user.corpuser.domain.user.QueryUserToResponse;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFActionType;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFCode;
import kh.com.bicbank.corp.user.shared.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Slf4j
@RestController
@Tag(name = "CDB Users Public API")
@RequestMapping("/api/v1/corp/users")
@RequiredArgsConstructor
public class PublicUserResource {

  private final AccountsApplicationService accountService;
  private final UserService userService;
  private final AccessControlApplicationService accessControlService;
  private final ApprovalService approvalService;

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        })
  })
  @GetMapping("")
  ResponseEntity<Response> getUserList(
      @RequestParam String customerId,
      @RequestParam(required = false, defaultValue = "") String search,
      @RequestParam(required = false, defaultValue = "") String userName,
      @RequestParam(required = false, defaultValue = "") String email,
      @RequestParam(required = false, defaultValue = "") String idNumber,
      @RequestParam(required = false, defaultValue = "") String phoneNumber,
      @RequestParam(required = false, defaultValue = "")  String status,
      RestCustomPageable customPageable) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    if (!account.realmName().get().equalsIgnoreCase("business-employee-portal")) {
      throw new ForbiddenException(Utils.makeResponse("CDBUSER_MESSAGE_013", null));
    }
    return ResponseEntity.ok(
        userService.getUserList(
            customerId,
            search,
            userName,
            email,
            idNumber,
            phoneNumber,
            status,
            customPageable));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUser.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "404",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        })
  })
  @GetMapping("/{userId}")
  ResponseEntity<Response> getUserDetail(@PathVariable String userId) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    if (!account.realmName().get().equalsIgnoreCase("business-employee-portal")) {
      throw new ForbiddenException(Utils.makeResponse("CDBUSER_MESSAGE_013", null));
    }
    System.out.println("account" + account);
    return ResponseEntity.ok(userService.getCorpUserDetail(userId));
  }

  @ApiResponses({
      @ApiResponse(
          responseCode = "200",
          content = {
              @Content(
                  schema = @Schema(implementation = RestUserToCreate.class),
                  mediaType = "application/json")
          }),
      @ApiResponse(
          responseCode = "400",
          content = {
              @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
          }),
      @ApiResponse(
          responseCode = "409",
          content = {
              @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
          }),
  })
  @Operation(summary = "Create new users")
  @PostMapping("")
  ResponseEntity<Response> createUser(
      @RequestBody RestUserToCreate request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.CREATE_USER.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }

    return ResponseEntity.ok(new Response(true, approvalService.postApprovalCreateUser(account, request), null));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUserToUpdate.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "404",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        })
  })
  @RequestMapping(value = "/{userId}",method = RequestMethod.PUT, consumes = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Update user information")
  ResponseEntity<Response> updateUserInfo(
      @PathVariable String userId,
      @RequestBody RestUserToUpdate request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.UPDATE_USER.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(
        new Response(
            true, approvalService.postApprovalUpdateUser(account, userId, request), null));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUpdateUserStatusRequest.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "404",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/lock")
  @Operation(summary = "Lock user")
  ResponseEntity<Response> lockUser(
      @PathVariable String userId, @RequestBody RestUpdateUserStatusRequest request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.LOCK_USER.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(new Response(true, approvalService.postApprovalLockUser(account, userId, request), null));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUpdateUserStatusRequest.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "404",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/unlock")
  @Operation(summary = "Unlock user")
  ResponseEntity<Response> unlockUser(
      @PathVariable String userId, @RequestBody RestUpdateUserStatusRequest request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.UNLOCK_USER.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(new Response(true, approvalService.postApprovalUnlockUser(account, userId, request), null));
  }

  @ApiResponses({
    @ApiResponse(
        responseCode = "200",
        content = {
          @Content(
              schema = @Schema(implementation = RestUpdateUserStatusRequest.class),
              mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "400",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        }),
    @ApiResponse(
        responseCode = "404",
        content = {
          @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
        })
  })
  @PostMapping("/{userId}/remove")
  @Operation(summary = "Remove user")
  ResponseEntity<Response> removeUser(
      @PathVariable String userId, @RequestBody RestUpdateUserStatusRequest request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.REMOVE_USER.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(new Response(true, approvalService.postApprovalRemoveUser(account, userId, request), null));
  }

  @ApiResponses({
      @ApiResponse(
          responseCode = "200",
          content = {
              @Content(
                  schema = @Schema(implementation = RestResetPasswordRequest.class),
                  mediaType = "application/json")
          }),
      @ApiResponse(
          responseCode = "404",
          content = {
              @Content(schema = @Schema(implementation = Error.class), mediaType = "application/json")
          })
  })
  @PostMapping("/{userId}/reset-password")
  @Operation(summary = "Reset password")
  ResponseEntity<Response> resetPassword(
      @PathVariable String userId, @RequestBody RestResetPasswordRequest request) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    Account account = optionalAccount.get();
    String bepUserId = account.userId().userId();
    String bfCode = BFCode.RESET_PASSWORD.getCode();
    if (!accessControlService.isPermitOnAction(
        bepUserId, bfCode, BFActionType.CREATE)) {
      throw new ForbiddenException();
    }
    if (accessControlService.isMakerSigning(bfCode, null)) {
      log.error("actionType: {} - bfCode: {} must be apply signing workflow", BFActionType.CREATE, bfCode);
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(new Response(true, approvalService.postApprovalResetPassword(account, userId, request), null));
  }

  @ApiResponses({
      @ApiResponse(
          responseCode = "200",
          content = {
              @Content(
                  schema = @Schema(implementation = RestUser.class),
                  mediaType = "application/json")
          }),
      @ApiResponse(
          responseCode = "409",
          content = {
              @Content(
                  schema = @Schema(implementation = RestUser.class),
                  mediaType = "application/json")
          })
  })
  @Operation(summary = "Check exist userName or email")
  @PostMapping("/is-exist")
  ResponseEntity<Response> isExist(@RequestBody RestIsExist request) {
    return ResponseEntity.ok(userService.isExist(request));
  }

  @ApiResponses({
      @ApiResponse(
          responseCode = "200",
          content = {
              @Content(
                  schema = @Schema(implementation = QueryUserToResponse.class),
                  mediaType = "application/json")
          })
  })
  @GetMapping("/query")
  ResponseEntity<Response> queryUser(
      @RequestParam(required = false, defaultValue = "") String customerId,
      @RequestParam String privilegeGroup) {
    boolean isCDBUser = true;
    if (customerId.isEmpty()) {
      Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
      customerId = optionalAccount.get().customerId().get();
      if (customerId.isEmpty()) {
        // Is BEP user
        isCDBUser = false;
      }
    }
    return ResponseEntity.ok(userService.queryUser(customerId, privilegeGroup.toUpperCase(), isCDBUser));
  }
  @GetMapping("/list-user-belong-customer")
  ResponseEntity<Response> getUserListBelongCustomerIdPublic(
          @RequestParam String customerId,
          @RequestParam(required = false, defaultValue = "") String search,
          @RequestParam(required = false, defaultValue = "") String userName,
          @RequestParam(required = false, defaultValue = "") String email,
          @RequestParam(required = false, defaultValue = "") String idNumber,
          @RequestParam(required = false, defaultValue = "") String phoneNumber,
          @RequestParam(required = false, defaultValue = "")  String status) {
    Optional<Account> optionalAccount = accountService.authenticatedUserAccount();
    if (optionalAccount.isEmpty()) {
      throw new ForbiddenException();
    }
    return ResponseEntity.ok(
            userService.getUserListOfCustomerId(
                    customerId,
                    search,
                    userName,
                    email,
                    idNumber,
                    phoneNumber,
                    status));
  }
}
