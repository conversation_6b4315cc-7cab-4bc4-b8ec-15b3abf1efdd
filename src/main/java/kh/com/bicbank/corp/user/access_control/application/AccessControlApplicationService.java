package kh.com.bicbank.corp.user.access_control.application;

import com.fasterxml.jackson.databind.ObjectMapper;
import kh.com.bicbank.corp.user.access_control.domain.BusinessProcessSetting;
import kh.com.bicbank.corp.user.access_control.domain.CheckPrivilegeResponse;
import kh.com.bicbank.corp.user.shared.enumeration.domain.BFActionType;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@AllArgsConstructor
public class AccessControlApplicationService {

  private final RestHandler restHandler;

  private final ObjectMapper objectMapper;

  public boolean isPermitOnAction(String userId, String bfCode, BFActionType actionType) {
    CheckPrivilegeResponse checkResponse = restHandler.checkPrivilegeOnAction(userId, bfCode, actionType);
    return Objects.nonNull(checkResponse) && checkResponse.getIsPermit();
  }

  public boolean isMakerSigning(String bfCode, String customerId) {
    // get BF setting
    // then check if BF does not allow many request per time, we check in pending queue
    // if there is a pending request we make user wait until the pending request is completed.
    BusinessProcessSetting bfSettings = restHandler.getBfSettings(bfCode, customerId);
    if (Objects.nonNull(bfSettings.isMakerSigning())) {
      return bfSettings.isMakerSigning();
    }
    return false;
  }
}
