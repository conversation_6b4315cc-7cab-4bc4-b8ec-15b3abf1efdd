package kh.com.bicbank.corp.user.corpuser.domain.bep;

import com.bic.efw.core.exception.BusinessException;
import com.bic.efw.core.pagination.domain.CustomPage;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPage;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftListUserClientResponseDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftTokenClientRequestDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftTokenClientResponseDto;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.bep.RestUserBep;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.client.microsoft.MicrosoftClientRest;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import kh.com.bicbank.corp.user.shared.rest.domain.User;
import kh.com.bicbank.corp.user.shared.util.Utils;
import kh.com.bicbank.corp.user.wire.config.MicrosoftProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class BepUserAzureService {
    private final MicrosoftClientRest microsoftClientRest;
    private final MicrosoftProperties microsoftProperties;
    private final BepRepository bepRepository;
    private final RestHandler restHandler;

    public String getToken() {
        MicrosoftTokenClientRequestDto microsoftClientRequestDto = MicrosoftTokenClientRequestDto.builder()
                .client_secret(microsoftProperties.getClient_secret())
                .client_id(microsoftProperties.getClient_id())
                .resource(microsoftProperties.getResource())
                .grant_type(microsoftProperties.getGrant_type())
                .build();
        MicrosoftTokenClientResponseDto microsoftClientResponseDto = microsoftClientRest.getToken(microsoftClientRequestDto);
        return microsoftClientResponseDto.getAccess_token();
    }

    public MicrosoftListUserClientResponseDto getUsers(String accessToken) {
        MicrosoftListUserClientResponseDto users = microsoftClientRest.getUsers(accessToken);
        return users;
    }

    public void saveUsers(BepUsesListDto bepUsesListDto) {
        bepRepository.saveAll(bepUsesListDto);
    }


    public Response getUserList(
            String search,
            String userName,
            String email,
            String phoneNumber,
            String status,
            String departmentCode,
            String employeeId,
            String branchCode,
            RestCustomPageable customPageable) {
        List<UserBepEntity> allUser = bepRepository.getAllUser(search,userName,email,phoneNumber,status,departmentCode,employeeId,branchCode);
        List<UserBepResponse> responseData;
        responseData = allUser.stream().map(UserBepResponse::from).toList();
        Page page =
                new PageImpl<>(
                        responseData, Pageable.ofSize(customPageable.getPageSize()), responseData.size());
        CustomPage customPage =
                CustomPage.builder(responseData)
                        .currentPage(page.getNumber())
                        .pageSize(page.getSize())
                        .totalElementsCount(page.getTotalElements())
                        .build();
        return new Response(true).setData(RestCustomPage.from(customPage, RestUserBep::from));
    }


    public Response getUserById(String userId) {
        UserBepEntity userBepEntity = bepRepository.findById(userId);
        if (Objects.isNull(userBepEntity)) {
            throw new BusinessException(Utils.makeResponse("BEP_USER_MESSAGE_001", null));
        }
        return new Response(true).setData(RestUserBep.from(UserBepResponse.from(userBepEntity)));
    }

    public void updateStatus(UserBepStatus status, List<String> userId) {
        bepRepository.updateStatus(status, userId);
    }

    public void updateUsersBepEnroll(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        bepRepository.updateUsersBepEnroll(bepTransactionDataDtoList);
    }


    public void updateUsersBepSuspend(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        bepRepository.updateUsersBepSuspend(bepTransactionDataDtoList);
    }

    public Response getUsersSameBranchAnhDepartment(String userId) {
        List<String> ls = new ArrayList<>();
        List<UserBepEntity> userBepEntity = bepRepository.getUsersSameBranchAnhDepartment(userId);
        if (userBepEntity.isEmpty()) {
            List<User> userBepEntityViaKeycloak = restHandler.getBepUserSameBranchAndDepartmentViaKeycloak(userId);
            for (User user : userBepEntityViaKeycloak) {
                ls.add(user.getId());
            }
        } else {
            for (UserBepEntity ub : userBepEntity) {
                ls.add(ub.getId());
            }
        }
        return new Response(true).setData(ls);
    }

    public void activeUsersBep(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        bepRepository.activeUsersBep(bepTransactionDataDtoList);
    }

    public UserBepEntity getBranchAndDepartmentOfUser(String userId){
       return bepRepository.findById(userId);
    }

    public Response getInfoUserFromListId(List<String> userId) {
        Map<String, String> emails = new HashMap<>();
        List<UserInfo> userInfos = new ArrayList<>();
        List<UserBepEntity> userBepEntity = bepRepository.getInfoUserFromListId(userId);
        if (!userBepEntity.isEmpty()) {
            for (UserBepEntity user : userBepEntity) {
                for (String id : userId) {
                    if (id.equals(user.getId())) {
                        UserInfo userInfo = UserInfo.builder().userId(id).email(user.getMail()).build();
                        userInfos.add(userInfo);
                    }
                }
            }
        }
        return new Response(true).setData(userInfos);
    }

    public Response getInfoUserFromListIdViaKeycloak(String userId) {
        List<String> listUserId = new ArrayList<String>(Arrays.asList(userId.split(",")));
        List<UserInfo> userInfos = new ArrayList<>();
        List<User> userBepEntity = restHandler.getUserIdsViaKeycloak(userId);
        if (!userBepEntity.isEmpty()) {
            for (User user : userBepEntity) {
                for (String id : listUserId) {
                    if (id.equals(user.getId())) {
                        UserInfo userInfo = UserInfo.builder().userId(id).email(user.getEmail()).build();
                        userInfos.add(userInfo);
                    }
                }
            }
        }
        return new Response(true).setData(userInfos);
    }
}
