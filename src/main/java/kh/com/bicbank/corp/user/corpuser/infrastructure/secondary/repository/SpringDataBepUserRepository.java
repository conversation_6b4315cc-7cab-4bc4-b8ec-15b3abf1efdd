package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository;


import kh.com.bicbank.corp.user.corpuser.domain.bep.*;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import kh.com.bicbank.corp.user.shared.util.MapperUtil;
import liquibase.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class SpringDataBepUserRepository implements BepRepository {

  private final JpaBepUserRepository repository;

  @Override
  public void saveAll(BepUsesListDto bepUsesListDto) {
    for (MicrosoftUserDto microsoftUserDto : bepUsesListDto.getValue()) {
      microsoftUserDto.setStatus(UserBepStatus.PENDING);
      UserBepEntity userBepEntity = MapperUtil.map(microsoftUserDto, UserBepEntity.class);
      repository.save(userBepEntity);
    }
  }

  @Override
  public List<UserBepEntity> getAllUser(String search, String userName, String email, String phoneNumber, String status, String departmentCode,String employeeId,String branchCode) {

    String s = !search.isEmpty() ? search.trim().toLowerCase() : "";
    String un = !userName.isEmpty() ? userName.trim().toLowerCase() :  null;
    String em = !email.isEmpty()  ? email.trim().toLowerCase() : null;
    String p = !phoneNumber.isEmpty() ? phoneNumber.trim() : null;
    String st = !status.isEmpty() ? status.trim() : null;
    String dp = !departmentCode.isEmpty() ? departmentCode.trim().toLowerCase() : null;
    String epl = !employeeId.isEmpty() ? employeeId.trim().toLowerCase() : null;
    String br = !branchCode.isEmpty() ? branchCode.trim().toLowerCase() : null;
    List<UserBepEntity> userBepEntityList = repository.getUsersBep(s, un, em, p, st, dp,epl,br);
    return userBepEntityList;
  }

  @Override
  public UserBepEntity findById(String id) {
    return repository.findById(id);
  }

  @Override
  public void updateStatus(UserBepStatus status,List<String> userId) {
    repository.updateStatus(status,userId);
  }

  @Override
  public void updateUsersBepEnroll(List<UserBepTransactionDataDto> usersBep) {
    for (UserBepTransactionDataDto ub : usersBep) {
      UserBepEntity userBepEntity = repository.findById(ub.getUserId());
      if (userBepEntity != null) {
        userBepEntity.setBranchCode(ub.getBranchCode());
        userBepEntity.setDepartmentCode(ub.getDepartmentCode());
        if (!StringUtil.isEmpty(ub.getJobRoleIds())) {
          userBepEntity.setJobRoleIds(ub.getJobRoleIds());
        }
        userBepEntity.setStatus(ub.getStatus());
        repository.save(userBepEntity);
      }
    }

  }

  @Override
  public void updateUsersBepSuspend(List<UserBepTransactionDataDto> usersBep) {
    for (UserBepTransactionDataDto ub : usersBep) {
      UserBepEntity userBepEntity = repository.findById(ub.getUserId());
      if (userBepEntity != null) {
        userBepEntity.setStatus(UserBepStatus.SUSPENDED);
        repository.save(userBepEntity);
      }
    }

  }

  @Override
  public List<UserBepEntity> getUsersSameBranchAnhDepartment(String id) {
    return repository.getUsersSameBranchAnhDepartment(id);
  }

  @Override
  public void activeUsersBep(List<UserBepTransactionDataDto> usersBep) {
    for (UserBepTransactionDataDto ub : usersBep) {
      UserBepEntity userBepEntity = repository.findById(ub.getUserId());
      if (userBepEntity != null) {
        userBepEntity.setStatus(UserBepStatus.ACTIVE);
        repository.save(userBepEntity);
      }
    }

  }

  @Override
  public List<UserBepEntity> getInfoUserFromListId(List<String> userIds) {
    return repository.getInfoUserFromListId(userIds);
  }


}
