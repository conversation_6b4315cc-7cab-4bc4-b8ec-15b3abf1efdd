package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository;


import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepStatus;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

interface JpaBepUserRepository extends JpaRepository<UserBepEntity, UUID> {

    @Query("SELECT u FROM UserBepEntity u WHERE u.id = :id")
    UserBepEntity findById(@Param("id") String id);

    @Query(value = """
                SELECT ue.*
                FROM bep_user ue
                WHERE (lower(ue.display_name) LIKE CONCAT('%', :search, '%')
                OR lower(ue.surname) LIKE CONCAT('%', :search, '%')
                OR lower(ue.given_name) LIKE CONCAT('%', :search, '%')
                OR lower(ue.employee_id) LIKE CONCAT('%', :search, '%')
                OR ue.mail LIKE CONCAT('%', :search, '%'))
                AND (:userName is null or lower(ue.display_name) LIKE CONCAT('%', :userName, '%'))
                AND (:email is null or ue.mail LIKE CONCAT('%', :email, '%'))
                AND (:phoneNumber is null or ue.mobile_phone LIKE CONCAT('%', :phoneNumber, '%'))
                AND (:status is null or ue.status LIKE CONCAT('%', :status, '%'))
                AND (:employeeId is null or lower(ue.employee_id) LIKE CONCAT('%', :employeeId, '%'))
                AND (:departmentCode is null or lower(ue.department_code) LIKE CONCAT('%', :departmentCode, '%'))
                AND (:branchCode is null or lower(ue.branch_code) LIKE CONCAT('%', :branchCode, '%'))
            """, nativeQuery = true)
    List<UserBepEntity> getUsersBep(String search, String userName, String email, String phoneNumber, String status,String departmentCode,String employeeId,String branchCode );



    @Modifying
    @Query(value = "UPDATE UserBepEntity u SET u.status = :status WHERE u.id IN (:userBepEntityList)")
    void updateStatus(UserBepStatus status, List<String> userBepEntityList);


    @Query(value = """
                SELECT a.*
                FROM bep_user a,
                (SELECT b.branch_code ,b.department_code
                FROM bep_user b where b.id = :id ) ue
                WHERE a.branch_code = ue.branch_code and a.department_code =ue.department_code;
              
            """, nativeQuery = true)
    List<UserBepEntity> getUsersSameBranchAnhDepartment(String id);


    @Query(nativeQuery =true,value = "SELECT * FROM bep_user as e WHERE e.id IN (:userIds)")
    List<UserBepEntity> getInfoUserFromListId(@Param("userIds") List<String> userIds);

}
