package kh.com.bicbank.corp.user.approval.domain;

import com.bic.efw.core.error.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import kh.com.bicbank.corp.user.shared.constant.domain.PatternConstant;
import kh.com.bicbank.corp.user.shared.enumeration.domain.ApprovalStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude()
public class ApprovalInformation {
  private String customerId;
  private String customerName;
  private String id;
  private String userId;
  private String userType;
  private String idpIssuer;

  private String bfCode;
  private String bfName;

  @JsonFormat(pattern = PatternConstant.DateTime.ISO_DATE_TIME_PATTERN)
  private LocalDateTime createdDate;

  @JsonFormat(pattern = PatternConstant.DateTime.ISO_DATE_TIME_PATTERN)
  private LocalDateTime submittedDate;

  @JsonFormat(pattern = PatternConstant.DateTime.ISO_DATE_TIME_PATTERN)
  private LocalDateTime processDate;

  private String createdBy;
  private String createdByName;

  private String submittedBy;
  private String submittedByName;

  private Object transactionData;
  private String remark;
  private ApprovalStatus status;

  private String approvalPolicyId;
  private String approvalPolicyName;

  private String lastApprovedBy;
  private String lastApprovedByName;

  private String confirmationId;

  private List<ApprovalRecordInformation> approvalRecords;

  private Object callBackResponse;

  private String bfgId;

  private String bfgName;

  private String bfgCode;

  private Object additionalData;

  private String branchCode;

  private Boolean isLastApproval;

  private BigDecimal amount;

  private BigDecimal targetAmount;

  @JsonFormat(pattern = PatternConstant.DateTime.ISO_DATE_TIME_PATTERN)
  private LocalDateTime expirationDate;

  public static ApprovalInformation from(ApprovalInformation approvalInformation) {
    Assert.notNull("approvalInformation", approvalInformation);
    return approvalInformation;
  }

}
