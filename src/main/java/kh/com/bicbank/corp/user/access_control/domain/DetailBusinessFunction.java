package kh.com.bicbank.corp.user.access_control.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DetailBusinessFunction {
    @JsonProperty("bfId")
    private String bfId;

    @JsonProperty("bfCode")
    private String bfCode;

    @JsonProperty("bfName")
    private String bfName;

    @JsonProperty("bfDesc")
    private String bfDesc;

    @JsonProperty("privileges")
    private String privilegeCodes;

    @JsonProperty("approvalRoles")
    private List<ApprovalRole> approvalRoles;
}
