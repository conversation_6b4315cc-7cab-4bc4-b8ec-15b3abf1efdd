package kh.com.bicbank.corp.user.corpuser.application.impl;

import com.bic.efw.core.exception.BusinessException;
import com.bic.efw.core.exception.ResourceNotFoundException;
import com.bic.efw.core.keycloak.KeycloakService;
import com.bic.efw.core.pagination.domain.CustomPage;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPage;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import jakarta.ws.rs.core.Response.Status;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import kh.com.bicbank.corp.user.corpuser.domain.callback.ChangePasswordCallback;
import kh.com.bicbank.corp.user.corpuser.domain.callback.UpdateUserStatusCallback;
import kh.com.bicbank.corp.user.corpuser.domain.customer.CustomerInformation;
import kh.com.bicbank.corp.user.corpuser.domain.user.*;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestIsExist;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestUser;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.TransactionEntity;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserEntity;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.JpaTransactionRepository;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.UserRepository;
import kh.com.bicbank.corp.user.shared.enumeration.domain.TransactionStatus;
import kh.com.bicbank.corp.user.shared.infrastructure.KeycloakBusinessEmployeePropsConfig;
import kh.com.bicbank.corp.user.shared.infrastructure.KeycloakBusinessPropsConfig;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakAttributesEnum;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakProperties;
import kh.com.bicbank.corp.user.shared.pagination.domain.PaginationSearchKeycloak;
import kh.com.bicbank.corp.user.shared.pagination.infrastructure.secondary.CustomSearchKeycloak;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import kh.com.bicbank.corp.user.shared.rest.domain.User;
import kh.com.bicbank.corp.user.shared.util.Utils;
import kh.com.bicbank.corp.user.shared.validator.infrastructure.primary.DataValidator;
import org.keycloak.admin.client.CreatedResponseUtil;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.RealmRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.keycloak.OAuth2Constants.CLIENT_CREDENTIALS;

@Service
public class UserServiceImpl implements UserService {
  private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

  private final UserRepository userRepository;
  private final KeycloakBusinessPropsConfig keycloakBusinessPropsConfig;
  private final KeycloakBusinessEmployeePropsConfig keycloakBusinessEmployeePropsConfig;
  private final JpaTransactionRepository jpaTransactionRepository;

  KeycloakProperties keycloakProperties;

  KeycloakService keycloakService;

  RestHandler restHandler;

  String CORP_REALM;
  String BEP_REALM;



  public UserServiceImpl(
          KeycloakProperties keycloakProperties,
          KeycloakService keycloakService,
          UserRepository userRepository,
          RestHandler restHandler,
          KeycloakBusinessPropsConfig keycloakBusinessPropsConfig,
          KeycloakBusinessEmployeePropsConfig keycloakBusinessEmployeePropsConfig,
          JpaTransactionRepository jpaTransactionRepository) {
    this.keycloakProperties = keycloakProperties;
    this.keycloakService = keycloakService;
    this.userRepository = userRepository;
    this.restHandler = restHandler;
    CORP_REALM = keycloakProperties.getRealmCDB();
    BEP_REALM = keycloakProperties.getRealmBEP();
    this.keycloakBusinessPropsConfig = keycloakBusinessPropsConfig;
    this.keycloakBusinessEmployeePropsConfig = keycloakBusinessEmployeePropsConfig;
    this.jpaTransactionRepository = jpaTransactionRepository;
  }

  Keycloak getInstance() {
    return KeycloakBuilder.builder()
            .grantType(CLIENT_CREDENTIALS)
            .serverUrl(keycloakBusinessPropsConfig.getAuthServerUrl())
            .realm(keycloakBusinessPropsConfig.getRealm())
            .clientId(keycloakBusinessPropsConfig.getResource())
            .clientSecret(keycloakBusinessPropsConfig.getCredentials().getSecret())
            .build();
  }

  Keycloak getBepInstance() {
    return KeycloakBuilder.builder()
            .grantType(CLIENT_CREDENTIALS)
            .serverUrl(keycloakBusinessEmployeePropsConfig.getAuthServerUrl())
            .realm(keycloakBusinessEmployeePropsConfig.getRealm())
            .clientId(keycloakBusinessEmployeePropsConfig.getResource())
            .clientSecret(keycloakBusinessEmployeePropsConfig.getCredentials().getSecret())
            .build();
  }


  @Override
  @Transactional
  public Response createUserWithValidator(UserToCreate request) {
    DataValidator.validator(request);

    return createUser(request);
  }

  @Transactional
  public Response createUser(UserToCreate request) {
    log.info("createUser: {}", request);
    String customerId = request.getCustomerId();
    String phoneNumber = request.getPhoneNumber();
    String address = request.getAddress();
    String email = request.getEmail();
    String firstName = request.getFirstName();
    String lastName = request.getLastName();
    String userName = request.getUserName();

    CustomerInformation customerInformation =
            restHandler.getCustomerInformationById(request.getCustomerId());

    String businessRegistrationNumber = customerInformation.getBusinessRegistrationNumber();
    String customerName = customerInformation.getBusinessName();

    Map<String, List<String>> attributes = new HashMap<>();
    attributes.put(
            KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE, Collections.singletonList(customerId));
    attributes.put(KeycloakAttributesEnum.CUSTOMER_NAME_ATTRIBUTE, Collections.singletonList(customerName));
    attributes.put(KeycloakAttributesEnum.MOBILE_ATTRIBUTE, Collections.singletonList(phoneNumber));
    attributes.put(KeycloakAttributesEnum.ADDRESS_ATTRIBUTE, Collections.singletonList(address));
    attributes.put(
            KeycloakAttributesEnum.FIRST_NAME_KHR_ATTRIBUTE,
            Collections.singletonList(request.getFirstNameKhr()));
    attributes.put(
            KeycloakAttributesEnum.LAST_NAME_KHR_ATTRIBUTE,
            Collections.singletonList(request.getLastNameKhr()));
    attributes.put(
            KeycloakAttributesEnum.IDENTIFY_TYPE_ATTRIBUTE,
            Collections.singletonList(request.getIdentifyType()));
    attributes.put(
            KeycloakAttributesEnum.ID_NUMBER_ATTRIBUTE,
            Collections.singletonList(request.getIdNumber()));
    attributes.put(
            KeycloakAttributesEnum.DOB_ATTRIBUTE, Collections.singletonList(request.getDob()));
    attributes.put(
            KeycloakAttributesEnum.GENDER_ATTRIBUTE, Collections.singletonList(request.getGender()));
    attributes.put(
            KeycloakAttributesEnum.JOB_TITLE_ATTRIBUTE,
            Collections.singletonList(request.getJobTitle()));
    attributes.put(
            KeycloakAttributesEnum.JOB_ROLE_ID_ATTRIBUTE,
            Collections.singletonList(request.getJobRoleId()));
    attributes.put(
            KeycloakAttributesEnum.DESCRIPTION_ATTRIBUTE,
            Collections.singletonList(request.getDescription()));
    attributes.put(
            KeycloakAttributesEnum.ISSUED_DATE_ATTRIBUTE,
            Collections.singletonList((request.getIssuedDate())));
    attributes.put(
            KeycloakAttributesEnum.EXPIRY_DATE_ATTRIBUTE,
            Collections.singletonList(request.getExpiredDate()));
    attributes.put(
            KeycloakAttributesEnum.BUSINESS_REGISTRATION_NUMBER_ATTRIBUTE,
            Collections.singletonList(businessRegistrationNumber));
    attributes.put(
            KeycloakAttributesEnum.MIGRATION_COMPLETED_ATTRIBUTE, Collections.singletonList("false"));
    attributes.put(
            KeycloakAttributesEnum.DEVICE_REGISTRATION_COMPLETED_ATTRIBUTE,
            Collections.singletonList("false"));

    // Define user
    UserRepresentation user = new UserRepresentationRequest(request);
    user.setAttributes(attributes);
    user.setEmail(email);
    user.setFirstName(firstName);
    user.setLastName(lastName);
    user.setEmailVerified(true);

    String userId;
    try (jakarta.ws.rs.core.Response response =
                 keycloakService.createUserResource(getInstance(), CORP_REALM, user)) {
      userId = null;
      if (response.getStatus() == Status.CREATED.getStatusCode()) {
        userId = CreatedResponseUtil.getCreatedId(response);
      } else {
        if (response.getStatus() == Status.CONFLICT.getStatusCode()) {
          throw new BusinessException("CORE_MESSAGE_051");
        }
      }
    }
    if (userId != null) {
      user.setId(userId);
      userRepository.save(UserEntity.from(userId, request));

      // Assign job role
      restHandler.assignJobRole(
              UUID.fromString(userId),
              customerId,
              Collections.singletonList(UUID.fromString(request.getJobRoleId())));
    }

    return new Response(true)
            .setData(
                    RestUser.from(
                            UserToResponse.from(
                                    keycloakService.getUserDetail(getInstance(), CORP_REALM, userId))));
  }

  @Override
  public Response getUserList(Integer pageNo, Integer pageSize) {
    try (Keycloak keycloak = getInstance()) {
      List<UserRepresentation> userRepresentations =
              keycloakService.getUsersResource(getInstance(), CORP_REALM);
      List<UserToResponse> responseData =
              userRepresentations.stream().map(UserToResponse::from).collect(Collectors.toList());

      return new Response(true).setData(responseData);
    } catch (Exception e) {
      log.error(e.getMessage());
    }

    return null;
  }

  @Override
  public Response getCorpUserDetail(String userId) {
    Keycloak keycloak = getInstance();
    return new Response(true)
            .setData(
                    RestUser.from(
                            UserToResponse.from(keycloakService.getUserDetail(keycloak, CORP_REALM, userId))));
  }

  @Override
  public Response getCorpUserInfo(String userId) {
    var userInfo = getInstance().realm(keycloakBusinessPropsConfig.getRealm())
            .users()
            .get(userId)
            .toRepresentation();
    UserToResponse userToResponse = UserToResponse.from(userInfo);
    CustomerInformation customerInformation = restHandler.getCustomerInformationById(userToResponse.getCustomerId());
    userToResponse.setGbshortName(customerInformation.getGbshortName());

    return new Response(true).setData(RestUser.from(userToResponse));
  }

  @Override
  public Response getUserInfoDetail(String userId) {
    Keycloak keycloak = getInstance();
    var userInfo = keycloak.realm(keycloakBusinessPropsConfig.getRealm())
            .users()
            .get(userId)
            .toRepresentation();
    return new Response(true).setData(userInfo);
  }

  @Override
  public Response updateUser(String userId, UserToUpdate request) {
    validateInfoUpdateUser(userId, request);

    return updateUserInfo(userId, request);
  }

  @Transactional
  public Response updateUserInfo(String userId, UserToUpdate request) {
    log.info("updateUserInfo: {} - userId: {}", request, userId);
    String phoneNumber = request.getPhoneNumber();
    String address = request.getAddress();

    // get user info existed
    UserRepresentation userRepresentation =
            keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);
    Map<String, List<String>> existingAttributes = userRepresentation.getAttributes();

    userRepresentation.setFirstName(request.getFirstName());
    userRepresentation.setLastName(request.getLastName());
    userRepresentation.setEmail(request.getEmail());
    Map<String, List<String>> attributes = new HashMap<>();
    for (Map.Entry<String, List<String>> entry : existingAttributes.entrySet()) {
      attributes.put(entry.getKey(), entry.getValue());
    }
    attributes.put(KeycloakAttributesEnum.MOBILE_ATTRIBUTE, Collections.singletonList(phoneNumber));
    attributes.put(KeycloakAttributesEnum.ADDRESS_ATTRIBUTE, Collections.singletonList(address));
    attributes.put(
            KeycloakAttributesEnum.FIRST_NAME_KHR_ATTRIBUTE,
            Collections.singletonList(request.getFirstNameKhr()));
    attributes.put(
            KeycloakAttributesEnum.LAST_NAME_KHR_ATTRIBUTE,
            Collections.singletonList(request.getLastNameKhr()));
    attributes.put(
            KeycloakAttributesEnum.DOB_ATTRIBUTE, Collections.singletonList(request.getDob()));
    attributes.put(
            KeycloakAttributesEnum.GENDER_ATTRIBUTE, Collections.singletonList(request.getGender()));
    attributes.put(
            KeycloakAttributesEnum.JOB_TITLE_ATTRIBUTE,
            Collections.singletonList(request.getJobTitle()));
    attributes.put(
            KeycloakAttributesEnum.JOB_ROLE_ID_ATTRIBUTE,
            Collections.singletonList(request.getJobRoleId()));
    attributes.put(
            KeycloakAttributesEnum.DESCRIPTION_ATTRIBUTE,
            Collections.singletonList(request.getDescription()));
    attributes.put(
            KeycloakAttributesEnum.ISSUED_DATE_ATTRIBUTE,
            Collections.singletonList((request.getIssuedDate())));
    attributes.put(
            KeycloakAttributesEnum.EXPIRY_DATE_ATTRIBUTE,
            Collections.singletonList(request.getExpiredDate()));

    userRepresentation.setAttributes(attributes);
    keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);

    // Assign job role
    restHandler.assignJobRole(
            UUID.fromString(userId),
            existingAttributes.get(KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE).get(0),
            Collections.singletonList(UUID.fromString(request.getJobRoleId())));
    restHandler.pushEmailNotifyWithKeycloak(userId);
    return new Response(true).setData(RestUser.from(UserToResponse.from(userRepresentation)));
  }

  @Override
  @Transactional
  public Response updateUserStatus(String userId, UpdateUserStatusCallback request) {
    log.info("updateUserStatus: {} for userId: {}", request, userId);
    String status = request.getStatus();
    if (!UserStatus.isMember(status)) {
      throw new BusinessException("CORE_MESSAGE_052");
    }

    UserRepresentation userRepresentation =
            keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);
    if (status != null) {
      if (status.equalsIgnoreCase(UserStatus.LOCKED.toString())
              || status.equalsIgnoreCase(UserStatus.INACTIVE.toString())) {
        userRepresentation.setEnabled(false);
      } else if (status.equalsIgnoreCase(UserStatus.ACTIVE.toString())) {
        // TODO check status block or inactive when unblock
        userRepresentation.setEnabled(true);
      } else {
        // TODO check invalid status
      }
    }

    keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);

    userRepository.updateStatusByIdpUserId(userId, UserStatus.valueOf(status));

    return new Response(true).setData(RestUser.from(UserToResponse.from(userRepresentation)));
  }

  @Override
  public Response getUserListByAttribute(String customerId, RestCustomPageable customPageable) {
    try (Keycloak keycloak = getInstance()) {
      PaginationSearchKeycloak pagination =
              new CustomSearchKeycloak().getPagination(customPageable);
      List<UserRepresentation> userRepresentations =
              keycloak
                      .realm(CORP_REALM)
                      .users()
                      .searchByAttributes(KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE + ":" + customerId);
      List<UserRepresentation> users = new ArrayList<>();
      for (UserRepresentation user : userRepresentations) {
        if (!user.isEnabled()) {
          List<UserEntity> userEntity = userRepository.findByIdpUserId(user.getId());
          if (!userEntity.isEmpty()) {
            if (!userEntity.get(0).getStatus().equals(UserStatus.INACTIVE)) {
              users.add(user);
            }
          }
        } else {
          users.add(user);
        }
      }
      List<UserToResponse> responseData =
              users.stream().map(UserToResponse::from).collect(Collectors.toList());
      Page page =
              new PageImpl<>(
                      userRepresentations,
                      Pageable.ofSize(customPageable.getPageSize()),
                      userRepresentations.size());
      CustomPage customPage =
              CustomPage.builder(responseData)
                      .currentPage(page.getNumber())
                      .pageSize(page.getSize())
                      .totalElementsCount(page.getTotalElements())
                      .build();
      return new Response(true).setData(RestCustomPage.from(customPage, RestUser::from));
    } catch (Exception e) {
      log.error(e.getMessage());
    }

    return null;
  }

  @Override
  @Transactional
  public Response validateInfoCreateUser(UserToCreate request) {
    DataValidator.validator(request);
    return Response.builder().success(true).build();
  }

  @Override
  @Transactional
  public Response validateInfoUpdateUser(String userId, UserToUpdate request) {
    UserRepresentation userRepresentation = null;
    try{
      userRepresentation =
              keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);
    } catch (ResourceNotFoundException ex) {
      throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_010", null));
    }

    DataValidator.validator(userRepresentation, request);
    return Response.builder().success(true).build();
  }

  @Override
  public void validateInfoUpdateUserStatus(String userId, String customerId) {
    UserRepresentation userRepresentation = null;
    try{
      userRepresentation =
              keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);
    } catch (ResourceNotFoundException ex) {
      throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_010", null));
    }
    DataValidator.isExistingCustomer(customerId);
  }

  @Override
  public void validateUserInfo(BindingResult bindingResult) {
    if (bindingResult.hasErrors()) {
      List<String> errorMsg = new ArrayList<>();

      bindingResult
              .getFieldErrors()
              .forEach(
                      error -> {
                        String errorStr =
                                "Field: " + error.getField() + ", reason: " + error.getDefaultMessage();
                        log.error(errorStr);
                        errorMsg.add(errorStr);
                      });
      log.info(errorMsg.toString());

      throw new BusinessException("CORE_MESSAGE_052");
    }
  }

  @Override
  public Response getBepUserDetail(String userId) {
    Keycloak keycloak = getBepInstance();
    return new Response(true)
            .setData(
                    RestUser.from(
                            UserToResponse.from(keycloakService.getUserDetail(keycloak, BEP_REALM, userId))));
  }

  @Override
  public Response getUserList(
          String customerId,
          String search,
          String userName,
          String email,
          String idNumber,
          String phoneNumber,
          String status,
          RestCustomPageable customPageable) {
    HashMap<String, String> params = new HashMap<>();
    params.put("customerId", customerId);
    params.put("keyword", search);
    params.put("userName", userName);
    params.put("email", email);
    params.put("idNumber", idNumber);
    params.put("phoneNumber", phoneNumber);
    List<User> userList = restHandler.searchUser(params);
    List<User> users = new ArrayList<>();
    for (User user : userList) {
      if (!user.getEnabled()) {
        List<UserEntity> userEntity = userRepository.findByIdpUserId(user.getId());
        if (!userEntity.isEmpty()) {
          if (!userEntity.get(0).getStatus().equals(UserStatus.INACTIVE)) {
            users.add(user);
          }
        }
      } else {
        users.add(user);
      }
    }

    List<UserToResponse> responseData;
    if (!status.isEmpty()) {
      responseData =
              users.stream()
                      .map(UserToResponse::from)
                      .filter(x -> x.getStatus().equals(status))
                      .toList();
    } else {
      responseData = users.stream().map(UserToResponse::from).toList();
    }

    Page page =
            new PageImpl<>(
                    responseData, Pageable.ofSize(customPageable.getPageSize()), responseData.size());
    CustomPage customPage =
            CustomPage.builder(responseData)
                    .currentPage(page.getNumber())
                    .pageSize(page.getSize())
                    .totalElementsCount(page.getTotalElements())
                    .build();
    return new Response(true).setData(RestCustomPage.from(customPage, RestUser::from));
  }

  public Response isExist(RestIsExist request) {
    Keycloak instance = getInstance();
    try {
      UsersResource usersResource = instance.realm(CORP_REALM).users();
      switch (request.getName().toUpperCase()) {
        case "USERNAME":
          if (!usersResource.searchByUsername(request.getValue(), true).isEmpty()) {
            return new Response(true)
                    .setData(IsExistResponse.builder().isExist(true).build())
                    .setError(Utils.makeError("CDBUSER_MESSAGE_001", null));
          }
          break;

        case "EMAIL":
          if (!usersResource.searchByEmail(request.getValue(), true).isEmpty()) {
            return new Response(true)
                    .setData(IsExistResponse.builder().isExist(true).build())
                    .setError(Utils.makeError("CDBUSER_MESSAGE_002", null));
          }
          break;

        default:
      }
    } catch (Exception e) {
      log.info(e.getMessage());
    } finally {
      instance.close();
    }
    return new Response(true)
            .setData(IsExistResponse.builder().isExist(false).build());
  }

  @Override
  public Response changePassword(String userId, ChangePasswordCallback request) {
    CredentialRepresentation passwordCred = new CredentialRepresentation();
    passwordCred.setTemporary(false);
    passwordCred.setType(CredentialRepresentation.PASSWORD);
    passwordCred.setValue(request.getNewPassword());

    UserRepresentation userRepresentation =
            keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);

    userRepresentation.setCredentials(Collections.singletonList(passwordCred));

    // Reset failedVerifyPasswordAttempt when change password success
    Map<String, List<String>> attributes = userRepresentation.getAttributes();
    attributes.put(KeycloakAttributesEnum.FAILED_VERIFY_PASSWORD_ATTEMPT_ATTRIBUTE, Arrays.asList("0"));
    userRepresentation.setAttributes(attributes);

    keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);
    return new Response(true);
  }

  @Override
  public Response getPasswordPolicy() {
    RealmResource realmResource = getInstance().realm(CORP_REALM);
    RealmRepresentation realmRepresentation = realmResource.toRepresentation();
    String passwordPolicyStr = realmRepresentation.getPasswordPolicy();
    List<Policy> policyList = new ArrayList<>();
    if (passwordPolicyStr != null) {
      List<String> policies = Arrays.asList(passwordPolicyStr.split(" and "));
      for (String policy : policies) {
        int start = policy.indexOf("(");
        int end = policy.indexOf(")");
        String name = policy.substring(0, start);
        String value = policy.substring(start + 1, end);
        policyList.add(new Policy(name, value));
      }
    }
    return new Response(true).setData(policyList);
  }

  @Override
  public Response queryUser(String customerId, String privilegeGroup, boolean isCDBUser) {
    if (isCDBUser) {
      HashMap<String, String> params = new HashMap<>();
      params.put("userType", "CUSTOMER");
      params.put("customerId", customerId);
      params.put("privilegeGroup", privilegeGroup);
      List<String> userIds = restHandler.queryUserWithPrivilege(params);
      List<QueryUserToResponse> userToResponses = new ArrayList<>();
      Keycloak instance = getInstance();
      try {
        List<UserRepresentation> userRepresentations =
                instance
                        .realm(CORP_REALM)
                        .users()
                        .searchByAttributes(
                                KeycloakAttributesEnum.CUSTOMER_ID_ATTRIBUTE + ":" + customerId);

        for (UserRepresentation user : userRepresentations) {
          if (userIds.contains(user.getId())) {
            userToResponses.add(QueryUserToResponse.from(user));
          }
        }
      } catch (Exception e) {
        log.info(e.getMessage());
      } finally {
        instance.close();
      }

      return new Response(true).setData(userToResponses);
    } else {
      HashMap<String, String> params = new HashMap<>();
      params.put("userType", "BANK");
      params.put("privilegeGroup", privilegeGroup);
      List<String> userIds = restHandler.queryUserWithPrivilege(params);
      List<QueryUserToResponse> userToResponses = new ArrayList<>();
      Keycloak instance = getBepInstance();
      try {
        List<UserRepresentation> userRepresentations =
                instance
                        .realm(BEP_REALM)
                        .users().list();

        for (UserRepresentation user : userRepresentations) {
          if (userIds.contains(user.getId())) {
            userToResponses.add(QueryUserToResponse.from(user));
          }
        }
      } catch (Exception e) {
        log.info(e.getMessage());
      } finally {
        instance.close();
      }

      return new Response(true).setData(userToResponses);
    }
  }
  @Override
  public Response getUserListOfCustomerId(
          String customerId,
          String search,
          String userName,
          String email,
          String idNumber,
          String phoneNumber,
          String status) {
    HashMap<String, String> params = new HashMap<>();
    params.put("customerId", customerId);
    params.put("keyword", search);
    params.put("userName", userName);
    params.put("email", email);
    params.put("idNumber", idNumber);
    params.put("phoneNumber", phoneNumber);
    List<User> userList = restHandler.searchUser(params);
    log.info("List user from keycloak");
    List<User> users = new ArrayList<>();
    for (User user : userList) {
      if (!user.getEnabled()) {
        List<UserEntity> userEntity = userRepository.findByIdpUserId(user.getId());
        log.info("User id : " + user.getId());
        if (!userEntity.isEmpty()) {
          if (!userEntity.get(0).getStatus().equals(UserStatus.INACTIVE)) {
            users.add(user);
          }
        }
      } else {
        users.add(user);
      }
    }

    List<UserToResponse> responseData;
    if (!status.isEmpty()) {
      responseData =
              users.stream()
                      .map(UserToResponse::from)
                      .filter(x -> x.getStatus().equals(status))
                      .toList();
    } else {
      responseData = users.stream().map(UserToResponse::from).toList();
    }
    log.info("List user done");
    return new Response(true).setData(responseData);
  }

  @Override
  @Transactional
  public void saveTransaction(Object requestData, UUID id) {
    TransactionEntity entity = TransactionEntity.builder()
            .id(id)
            .request(requestData)
            .status(TransactionStatus.DRAFT)
            .processedDate(LocalDateTime.now())
            .createdDate(LocalDateTime.now())
            .build();
    jpaTransactionRepository.save(entity);
  }

}
