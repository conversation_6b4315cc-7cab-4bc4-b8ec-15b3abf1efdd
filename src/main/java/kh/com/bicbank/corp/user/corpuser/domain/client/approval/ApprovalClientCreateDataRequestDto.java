package kh.com.bicbank.corp.user.corpuser.domain.client.approval;


import com.fasterxml.jackson.annotation.JsonProperty;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepTransactionDataDto;
import kh.com.bicbank.corp.user.shared.enumeration.domain.CallBackMethod;
import kh.com.bicbank.corp.user.shared.enumeration.domain.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApprovalClientCreateDataRequestDto {

    @JsonProperty("customerId")
    private UUID customerId;

    @JsonProperty("customerName")
    private String customerName;

    @JsonProperty("userId")
    private UUID userId;

    @JsonProperty("username")
    private String username;

    @JsonProperty("userType")
    private UserType userType;

    @JsonProperty("idpIssuer")
    private String idpIssuer;

    @JsonProperty("bfCode")
    private String bfCode;

    @JsonProperty("bfName")
    private String bfName;

    @JsonProperty("remark")
    private String remark;

    @JsonProperty("transactionData")
    private List<UserBepTransactionDataDto> transactionData;

    @JsonProperty("callBackUrl")
    private String callBackUrl;

    @JsonProperty("callbackMethod")
    private CallBackMethod callbackMethod;

    @JsonProperty("bfgId")
    private String bfgId;

    @JsonProperty("bfgName")
    private String bfgName;

    @JsonProperty("bfgCode")
    private String bfgCode;

    @JsonProperty("additionalData")
    private Object additionalData;

    @JsonProperty("fullName")
    private String fullName;

    @JsonProperty("branchCode")
    private String branchCode;

    @JsonProperty("departmentCode")
    private String departmentCode;


}
