package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository;

import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.UUID;

public interface JpaUserRepository extends JpaRepository<UserEntity, UUID> {

  @Query("SELECT u FROM UserEntity u WHERE u.idpUserId = :userId")
  List<UserEntity> findByIdpUserId(String userId);

  @Query("SELECT u FROM UserEntity u WHERE u.id = :id")
  UserEntity findById(@Param("id") String id);

}
