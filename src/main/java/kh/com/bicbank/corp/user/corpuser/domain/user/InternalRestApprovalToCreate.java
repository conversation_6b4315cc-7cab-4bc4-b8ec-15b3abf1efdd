package kh.com.bicbank.corp.user.corpuser.domain.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(name = "internal approvalToCreate", description = "An Approval to create")
public class InternalRestApprovalToCreate {

  @JsonProperty("customerId")
  private String customerId;

  @JsonProperty("customerName")
  private String customerName;

  @JsonProperty("userId")
  private String userId;

  @JsonProperty("username")
  private String username;

  @JsonProperty("userType")
  private String userType;

  @JsonProperty("idpIssuer")
  private String idpIssuer;

  @JsonProperty("bfCode")
  private String bfCode;

  @JsonProperty("bfName")
  private String bfName;

  @Size(max = 500)
  @JsonProperty("remark")
  private String remark;

  @JsonProperty("transactionData")
  private Object transactionData;

  @JsonProperty("callBackUrl")
  private String callBackUrl;

  @JsonProperty("bfgId")
  private String bfgId;

  @JsonProperty("bfgName")
  private String bfgName;

  @JsonProperty("bfgCode")
  private String bfgCode;

  @JsonProperty("additionalData")
  private Object additionalData;

  @JsonProperty("branchCode")
  private String branchCode;

  @JsonProperty("departmentCode")
  private String departmentCode;

  @JsonProperty("fullName")
  private String fullName;

  @JsonProperty("templateContent")
  private Map<String, Object> templateContent;

}
