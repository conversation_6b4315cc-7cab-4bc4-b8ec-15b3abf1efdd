package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import kh.com.bicbank.corp.user.corpuser.application.CallbackService;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import kh.com.bicbank.corp.user.corpuser.domain.callback.ChangePasswordCallback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

@RestController
@Tag(name = "Internal callback")
@RequestMapping("/internal/api/users")
@RequiredArgsConstructor
@Hidden
@Slf4j
public class UserIdentitiesCallbackResource {
    private final UserService userService;
    private final CallbackService callbackService;

    @PostMapping("/identities/password/callback/{userId}/confirmation")
    @Operation(summary = "Change Password")
    @ApiResponse(description = "This endpoint is used to change password." + "\n", responseCode = "200")
    ResponseEntity changePasswordCallback(@PathVariable("userId") String userId, @RequestBody @Validated ChangePasswordCallback request,
                                          @RequestParam(value = "action", required = false) String action) {
        log.info("changePasswordCallback ".concat(request.toString()));
        if (StringUtils.isEmpty(userId)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "UserId id is missing");
        }

        if ("CONFIRMED".equals(action)) {
            this.userService.changePassword(userId, request);
        }

        this.userService.changePassword(userId, request);
        return new ResponseEntity(HttpStatus.OK);
    }
}
