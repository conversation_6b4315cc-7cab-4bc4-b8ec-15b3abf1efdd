package kh.com.bicbank.corp.user.corpuser.application;

import com.bic.efw.core.exception.BusinessException;
import com.bic.efw.core.pagination.infrastructure.primary.RestCustomPageable;
import com.bic.efw.core.payload.Response;
import kh.com.bicbank.corp.user.corpuser.domain.bep.BepUserAzureService;
import kh.com.bicbank.corp.user.corpuser.domain.bep.BepUsesListDto;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepStatus;
import kh.com.bicbank.corp.user.corpuser.domain.bep.UserBepTransactionDataDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.access.AccessControlClient;
import kh.com.bicbank.corp.user.corpuser.domain.client.access.UserJobRolesListDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.access.UserJobRolesListRequest;
import kh.com.bicbank.corp.user.corpuser.domain.client.approval.ApprovalClient;
import kh.com.bicbank.corp.user.corpuser.domain.client.approval.ApprovalClientCreateDataRequestDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.approval.ApprovalClientResponseDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftListUserClientResponseDto;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.UserBepEntity;
import kh.com.bicbank.corp.user.wire.config.ApprovalProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class BepUserService {
    private final BepUserAzureService userMicrosoftService;
    private final ApprovalClient approvalClient;
    private final ApprovalProperties approvalProperties;

    private final AccessControlClient accessControlClient;


    public String getToken() {
        return userMicrosoftService.getToken();
    }

    public MicrosoftListUserClientResponseDto getUsers(String accessToken) {
        return userMicrosoftService.getUsers(accessToken);
    }
    @Transactional()
    public void saveUsers(BepUsesListDto bepUsesListDto){
        userMicrosoftService.saveUsers(bepUsesListDto);
    }

    public Response getUserList(String search, String userName, String email, String phoneNumber, String status,String departmentCode,String employeeId,String branchCode, RestCustomPageable customPageable) {
        return userMicrosoftService.getUserList(search, userName, email, phoneNumber, status, departmentCode,employeeId,branchCode, customPageable);
    }

    public Response getUserById(String userId) {
        return userMicrosoftService.getUserById(userId);
    }

    @Transactional()
    public void updateStatus(UserBepStatus status, List<String> userId) {
        userMicrosoftService.updateStatus(status,userId);
    }
    public void validateEnrollUserBep(List<UserBepTransactionDataDto> transactionData) {
        if (Objects.isNull(transactionData)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, new Response().setError("CORE_MESSAGE_026", "TransactionData id is missing"));
        }

    }
    public ApprovalClientResponseDto createRequestEnrollUserBep(ApprovalClientCreateDataRequestDto request) {
        //Call Approval Service to create request
        request.setCallBackUrl(approvalProperties.getCallBackUrlEnroll());
        ApprovalClientResponseDto responseCreate = approvalClient.createRequestUserBep(request);
        if(!Objects.isNull(responseCreate) && !responseCreate.isSuccess()){
            throw new BusinessException("CORE_MESSAGE_044");
        }
        return responseCreate;
    }

    public ApprovalClientResponseDto createRequestSuspendUserBep(ApprovalClientCreateDataRequestDto request) {
        //Call Approval Service to create request
        request.setCallBackUrl(approvalProperties.getCallBackUrlSuspend());
        ApprovalClientResponseDto responseCreate = approvalClient.createRequestUserBep(request);
        if(!Objects.isNull(responseCreate) && !responseCreate.isSuccess()){
            throw new BusinessException("CORE_MESSAGE_044");
        }
        return responseCreate;
    }

    @Transactional()
    public void updateUsersBepEnroll(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        userMicrosoftService.updateUsersBepEnroll(bepTransactionDataDtoList);
    }

    @Transactional()
    public void updateUsersBepSuspend(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        userMicrosoftService.updateUsersBepSuspend(bepTransactionDataDtoList);
    }

    public Response getUsersSameBranchAnhDepartment(String userId) {
        return userMicrosoftService.getUsersSameBranchAnhDepartment(userId);
    }
    public ApprovalClientResponseDto createRequestUpdateUserBep(ApprovalClientCreateDataRequestDto request) {
        //Call Approval Service to create request
        request.setCallBackUrl(approvalProperties.getCallBackUrlEnroll());
        ApprovalClientResponseDto responseCreate = approvalClient.createRequestUserBep(request);
        if(!Objects.isNull(responseCreate) && !responseCreate.isSuccess()){
            throw new BusinessException("CORE_MESSAGE_044");
        }
        return responseCreate;
    }

    public ApprovalClientResponseDto createRequestActiveUserBep(ApprovalClientCreateDataRequestDto request) {
        //Call Approval Service to create request
        request.setCallBackUrl(approvalProperties.getCallBackUrlActive());
        ApprovalClientResponseDto responseCreate = approvalClient.createRequestUserBep(request);
        if(!Objects.isNull(responseCreate) && !responseCreate.isSuccess()){
            throw new BusinessException("CORE_MESSAGE_044");
        }
        return responseCreate;
    }
    @Transactional()
    public void activeUsersBep(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {
        userMicrosoftService.activeUsersBep(bepTransactionDataDtoList);
    }

    public void assignJobRoles(List<UserBepTransactionDataDto> bepTransactionDataDtoList) {

        UserJobRolesListRequest userJobRolesListRequest = new UserJobRolesListRequest();
        List<UserJobRolesListDto> userJobRolesListDtos = new ArrayList<>();
        for (UserBepTransactionDataDto users : bepTransactionDataDtoList) {
            UserJobRolesListDto userJobRolesListDto = new UserJobRolesListDto();
            userJobRolesListDto.setUserId(users.getUserId());
            if(users.getJobRoleIds().isEmpty()){
                userJobRolesListDto.setJobRoleIds(new ArrayList<>());
            }else{
                List<String> roleIds = new ArrayList<String>(Arrays.asList(users.getJobRoleIds().split(",")));
                userJobRolesListDto.setJobRoleIds(roleIds);
            }

            userJobRolesListDtos.add(userJobRolesListDto);
        }
        if(CollectionUtils.isNotEmpty(userJobRolesListDtos)){
            userJobRolesListRequest.setUserJobRolesList(userJobRolesListDtos);
            accessControlClient.assignRoles(userJobRolesListRequest);
        }



    }
    @Transactional()
    public UserBepEntity getBranchAndDepartmentOfUser(String userId){
        return userMicrosoftService.getBranchAndDepartmentOfUser(userId);
    }

    @Transactional()
    public Response getInfoUserFromListId(List<String> userId){
        return userMicrosoftService.getInfoUserFromListId(userId);
    }

    @Transactional()
    public Response getInfoUserFromListIdViaKeycloak(String userId){
        return userMicrosoftService.getInfoUserFromListIdViaKeycloak(userId);
    }



}
