package kh.com.bicbank.corp.user.corpuser.application;

import com.bic.efw.core.exception.BusinessException;
import com.bic.efw.core.keycloak.KeycloakService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.ws.rs.NotFoundException;
import kh.com.bicbank.corp.user.corpuser.domain.callback.ChangePasswordCallback;
import kh.com.bicbank.corp.user.corpuser.domain.client.confirmation.*;
import kh.com.bicbank.corp.user.corpuser.domain.client.device.DeviceManagementClient;
import kh.com.bicbank.corp.user.corpuser.domain.user.ChangePassword;
import kh.com.bicbank.corp.user.corpuser.domain.user.ChangePasswordResponseDto;
import kh.com.bicbank.corp.user.corpuser.domain.user.UserStatus;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.UserRepository;
import kh.com.bicbank.corp.user.shared.infrastructure.KeycloakBusinessPropsConfig;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakAttributesEnum;
import kh.com.bicbank.corp.user.shared.keycloak.infrastructure.KeycloakProperties;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import kh.com.bicbank.corp.user.shared.util.Utils;
import kh.com.bicbank.corp.user.wire.config.UserIdentityProperties;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.keycloak.OAuth2Constants.CLIENT_CREDENTIALS;

@Service
public class CorpUserService {
  private static final Logger log = LoggerFactory.getLogger(CorpUserService.class);
  private final KeycloakProperties keycloakProperties;
  private final UserIdentityProperties userIdentityProperties;
  private final UserService userService;
  private final KeycloakService keycloakService;
  private final ConfirmationClient confirmationClient;
  private final DeviceManagementClient deviceManagementClient;
  private final UserRepository userRepository;
  private final ObjectMapper objectMapper;
  private final String CORP_REALM;
  private final RestHandler restHandler;
  private final KeycloakBusinessPropsConfig keycloakBusinessPropsConfig;

  public CorpUserService(
          KeycloakProperties keycloakProperties,
          UserIdentityProperties userIdentityProperties,
          UserService userService,
          KeycloakService keycloakService,
          ConfirmationClient confirmationClient,
          DeviceManagementClient deviceManagementClient,
          UserRepository userRepository, ObjectMapper objectMapper,
          RestHandler restHandler,
          KeycloakBusinessPropsConfig keycloakBusinessPropsConfig) {
    this.keycloakProperties = keycloakProperties;
    this.userIdentityProperties = userIdentityProperties;
    this.userService = userService;
    this.keycloakService = keycloakService;
    this.confirmationClient = confirmationClient;
    this.deviceManagementClient = deviceManagementClient;
    this.userRepository = userRepository;
    this.objectMapper = objectMapper;
    this.CORP_REALM = keycloakProperties.getRealmCDB();
    this.restHandler = restHandler;
    this.keycloakBusinessPropsConfig = keycloakBusinessPropsConfig;
  }

  Keycloak getInstance() {
    return KeycloakBuilder.builder()
            .grantType(CLIENT_CREDENTIALS)
            .serverUrl(keycloakBusinessPropsConfig.getAuthServerUrl())
            .realm(keycloakBusinessPropsConfig.getRealm())
            .clientId(keycloakBusinessPropsConfig.getResource())
            .clientSecret(keycloakBusinessPropsConfig.getCredentials().getSecret())
            .build();
  }

  @Transactional
  public boolean verifyExistingPassword(String userId, String password) {
    Keycloak instance = getInstance();
    try {
      UserResource userResource = instance.realm(CORP_REALM).users().get(userId);
      String userName = userResource.toRepresentation().getUsername();

      // Verify valid user verify password attempt
      Map<String, List<String>> userAttribute =
              userResource.toRepresentation().getAttributes();

      String attributeName = KeycloakAttributesEnum.FAILED_VERIFY_PASSWORD_ATTEMPT_ATTRIBUTE;
      if (userAttribute.containsKey(attributeName)) {
        String existingValue = userAttribute.get(attributeName).get(0);
        Integer number = Integer.parseInt(existingValue);
        int compare = number.compareTo(userIdentityProperties.getMaxVerifyPasswordAttempt());
        if (compare >= 0) {
          log.info("failed verify password attempt for userName {} - attemptsNumber {}", userName, number);
          throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_012", null));
        }
      }

      boolean isVerifyPassword = keycloakGetInstanceWithUserNameAndPassword(userName, password);
      log.info("verify password for userId: {} - userName: {} - isVerifyPassword: {}", userId, userName, isVerifyPassword);

      if (!isVerifyPassword) {
        updateMaxInvalidVerifyPasswordAttemptAttribute(userId);
      } else {
        UserRepresentation userRepresentation = userResource.toRepresentation();
        // Reset failedVerifyPasswordAttempt when verify password passed
        Map<String, List<String>> attributes = userRepresentation.getAttributes();
        attributes.put(KeycloakAttributesEnum.FAILED_VERIFY_PASSWORD_ATTEMPT_ATTRIBUTE, Arrays.asList("0"));
        userRepresentation.setAttributes(attributes);

        keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);
      }

      return isVerifyPassword;
    } catch (NotFoundException ex) {
      throw new BusinessException("CORE_MESSAGE_046");
    } finally {
      instance.close();
    }
  }

  @Transactional
  public void updateMaxInvalidVerifyPasswordAttemptAttribute(String userId) {
    log.info("updateMaxInvalidVerifyPasswordAttemptAttribute - userId: {}", userId);
    UserRepresentation userRepresentation =
            keycloakService.getUserDetail(getInstance(), CORP_REALM, userId);
    Map<String, List<String>> existingAttributes = userRepresentation.getAttributes();

    String attributeName = KeycloakAttributesEnum.FAILED_VERIFY_PASSWORD_ATTEMPT_ATTRIBUTE;
    if (existingAttributes.containsKey(attributeName)) {
      String existingValue = existingAttributes.get(attributeName).get(0);
      Integer number = Integer.parseInt(existingValue) + 1;
      existingAttributes.put(attributeName, Collections.singletonList(String.valueOf(number)));
      int compare = number.compareTo(userIdentityProperties.getMaxVerifyPasswordAttempt());
      if (compare >= 0) {
        log.info("when maximum verify password attempt - lock user {}", userRepresentation.getUsername());
        userRepresentation.setEnabled(false);
        userRepresentation.setAttributes(existingAttributes);
        keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);
        userRepository.updateStatusByIdpUserId(userId, UserStatus.LOCKED);

        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_012", null));
      }
    } else {
      log.info("set {} value when first verify failed", attributeName);
      existingAttributes.put(attributeName, Collections.singletonList(String.valueOf(1)));
    }

    userRepresentation.setAttributes(existingAttributes);
    keycloakService.updateUser(getInstance(), CORP_REALM, userRepresentation);
  }

  private boolean keycloakGetInstanceWithUserNameAndPassword(String username, String password) {
    Keycloak instance =
            keycloakService.getInstance(
                    keycloakProperties.getBaseUrl(),
                    keycloakProperties.getRealmCDB(),
                    username,
                    password,
                    keycloakProperties.getClientId());
    try {
      if (instance.tokenManager().getAccessToken() != null) {
        return true;
      }
    } catch (Exception ex) {
      log.error(ex.getMessage());
    } finally {
      instance.close();
    }

    return false;
  }

  public ChangePasswordResponseDto postConfirmation(String userId, ChangePassword request) {
    // checking password policies
    if (!restHandler.verifyPasswordPolicy(userId, request.getNewPassword())) {
      throw new BusinessException("password is not satisfy BIC policy");
    }
    ConfirmationClientRequestDto confirmationClientRequestDto = new ConfirmationClientRequestDto();
    confirmationClientRequestDto.setDbsUserId(userId);
    confirmationClientRequestDto.setIdpUserId(userId);
    confirmationClientRequestDto.setConfirmationType("change-password");
    confirmationClientRequestDto.setCallbackUrl(
            String.format(
                    userIdentityProperties.getEndpoint()
                            + "/internal/api/users/identities/password/callback/%s/confirmation",
                    userId));
    ConfirmationClientTransactionDataRequestDto transactionDataRequestDto =
            new ConfirmationClientTransactionDataRequestDto();
    transactionDataRequestDto.setUserId(userId);
    transactionDataRequestDto.setAction("Change Password");

    ChangePasswordCallback callbackData =
            ChangePasswordCallback.builder().newPassword(request.getNewPassword()).build();
    confirmationClientRequestDto.setTransactionData(callbackData);
    ConfirmationClientResponseDto confirmationClientResponseDto =
            confirmationClient.postConfirmation(confirmationClientRequestDto);

    ChangePasswordResponseDto changePasswordResponseDto = new ChangePasswordResponseDto();
    changePasswordResponseDto.setConfirmationId(confirmationClientResponseDto.getId());
    return changePasswordResponseDto;
  }

  public void approveTransaction(String transactionId, String userId, TransactionState transactionState) {
    CompleteTransactionSigningRequestDTO  completeTransactionSigningRequestDTO = new CompleteTransactionSigningRequestDTO();
    completeTransactionSigningRequestDTO.setTransactionId(transactionId);
    completeTransactionSigningRequestDTO.setUserId(userId);
    completeTransactionSigningRequestDTO.setState(transactionState);
    confirmationClient.approveTransaction(completeTransactionSigningRequestDTO);
  }

  public void removeUserDevice(String userId) {
    deviceManagementClient.removeUserDevice(userId);
  }
}
