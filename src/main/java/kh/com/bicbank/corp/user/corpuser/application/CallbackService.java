package kh.com.bicbank.corp.user.corpuser.application;

import com.bic.efw.core.exception.BusinessException;
import com.bic.efw.core.payload.Response;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import kh.com.bicbank.corp.user.corpuser.domain.callback.*;
import kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user.RestCallbackRequest;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.entity.TransactionEntity;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.JpaTransactionRepository;
import kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.repository.TransactionRepository;
import kh.com.bicbank.corp.user.shared.enumeration.domain.TransactionStatus;
import kh.com.bicbank.corp.user.shared.rest.RestHandler;
import kh.com.bicbank.corp.user.shared.util.JsonUtils;
import kh.com.bicbank.corp.user.shared.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class CallbackService {

  UserService userService;
  private final ObjectMapper objectMapper;
  private final JsonUtils jsonUtils;
  private final RestHandler restHandler;
  private final TransactionRepository transactionRepository;
  private final JpaTransactionRepository jpaTransactionRepository;

  public CallbackService(UserService userService, ObjectMapper objectMapper, JsonUtils jsonUtils,
                         RestHandler restHandler, TransactionRepository transactionRepository,
                         JpaTransactionRepository jpaTransactionRepository) {
    this.userService = userService;
    this.objectMapper = objectMapper;
    this.jsonUtils = jsonUtils;
    this.restHandler = restHandler;
      this.transactionRepository = transactionRepository;
      this.jpaTransactionRepository = jpaTransactionRepository;
  }

  public Response createUser(RestCallbackRequest request) {
    log.info("createUser request {}", jsonUtils.writeObjectAsJson(request));
    String status = request.getStatus();
    if (!RequestStatus.isMember(status)) {
      throw new BusinessException("CORE_MESSAGE_052");
    }
    if (status.equals(RequestStatus.CONFIRMED.name()) || status.equals(RequestStatus.APPROVED.name())) {
      Object transactionData = request.getTransactionData();
      InternalCreateUserCallback internalCallback =
          objectMapper.convertValue(transactionData, new TypeReference<>() {});

      Optional<TransactionEntity> transactionEntityOptional = jpaTransactionRepository.findById(internalCallback.getTransactionId());
      if (transactionEntityOptional.isEmpty()) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_014", null));
      }
      TransactionEntity transactionEntity = transactionEntityOptional.get();

      if (Objects.equals(transactionEntity.getStatus(), TransactionStatus.COMPLETED)) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_016", null));
      }
      if (!Objects.equals(transactionData, transactionEntity.getRequest())) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_015", null));
      }

      Response result = userService.createUserWithValidator(internalCallback.getUserToCreate());
      if (result.isSuccess()) {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.COMPLETED));
      } else {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.FAILED));
      }
      return result;
    } else {
      return new Response(true).setData(status);
    }
  }

  public Response updateUserStatus(String userId, RestCallbackRequest request) {
    log.info("updateUserStatus userId: {} - request: {}", userId, jsonUtils.writeObjectAsJson(request));
    String status = request.getStatus();
    if (!RequestStatus.isMember(status)) {
      throw new BusinessException("CORE_MESSAGE_052");
    }
    if (status.equals(RequestStatus.CONFIRMED.name()) || status.equals(RequestStatus.APPROVED.name())) {
      Object transactionData = request.getTransactionData();
      InternalUpdateUserStatusCallback internalCallback =
          objectMapper.convertValue(transactionData, InternalUpdateUserStatusCallback.class);

      Optional<TransactionEntity> transactionEntityOptional = jpaTransactionRepository.findById(internalCallback.getTransactionId());
      if (transactionEntityOptional.isEmpty()) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_014", null));
      }
      TransactionEntity transactionEntity = transactionEntityOptional.get();

      if (Objects.equals(transactionEntity.getStatus(), TransactionStatus.COMPLETED)) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_016", null));
      }
      if (!Objects.equals(transactionData, transactionEntity.getRequest())) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_015", null));
      }

      Response result = userService.updateUserStatus(userId, internalCallback.getUpdateUserStatusCallback());
      if (result.isSuccess()) {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.COMPLETED));
      } else {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.FAILED));
      }
      return result;
    } else {
      return new Response(true).setData(status);
    }
  }

  public Response updateUserInformation(String userId, RestCallbackRequest request) {
    log.info("updateUserInformation userId: {} - request: {}", userId, jsonUtils.writeObjectAsJson(request));
    String status = request.getStatus();
    if (!RequestStatus.isMember(status)) {
      throw new BusinessException("CORE_MESSAGE_052");
    }
    if (status.equals(RequestStatus.CONFIRMED.name()) || status.equals(RequestStatus.APPROVED.name())) {
      Object transactionData = request.getTransactionData();
      InternalUpdateUserCallback internalCallback =
          objectMapper.convertValue(transactionData, new TypeReference<>() {});

      Optional<TransactionEntity> transactionEntityOptional = jpaTransactionRepository.findById(internalCallback.getTransactionId());
      if (transactionEntityOptional.isEmpty()) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_014", null));
      }
      TransactionEntity transactionEntity = transactionEntityOptional.get();

      if (Objects.equals(transactionEntity.getStatus(), TransactionStatus.COMPLETED)) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_016", null));
      }
      if (!Objects.equals(transactionData, transactionEntity.getRequest())) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_015", null));
      }

      Response result = userService.updateUser(userId, internalCallback.getUserToUpdate());
      if (result.isSuccess()) {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.COMPLETED));
      } else {
        transactionRepository.save(transactionEntity.setStatus(TransactionStatus.FAILED));
      }
      return result;
    } else {
      return new Response(true).setData(status);
    }
  }

  public Response changePassword(String userId, RestCallbackRequest request) {
    String status = request.getStatus();
    if (!RequestStatus.isMember(status)) {
      throw new BusinessException("CORE_MESSAGE_052");
    }
    if (status.equals(RequestStatus.CONFIRMED.name()) || status.equals(RequestStatus.APPROVED.name())) {
      Object transactionData = request.getTransactionData();
      ChangePasswordCallback callbackData =
          objectMapper.convertValue(transactionData, new TypeReference<>() {});

      return userService.changePassword(userId, callbackData);
    } else {
      return new Response(true).setData(status);
    }
  }

  public Response resetPassword(String userId, RestCallbackRequest request) {
      log.info("resetPassword userId: {} - request: {}", userId, jsonUtils.writeObjectAsJson(request));
      String status = request.getStatus();
      if (! RequestStatus.isMember(status)) {
        throw new BusinessException("CORE_MESSAGE_052");
      }

      InternalResetPasswordCallback resetPasswordCallback =
              objectMapper.convertValue(request.getTransactionData(), new TypeReference<>() {
              });

      Optional<TransactionEntity> transactionEntityOptional = jpaTransactionRepository.findById(resetPasswordCallback.getTransactionId());
      if (transactionEntityOptional.isEmpty()) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_014", null));
      }
      TransactionEntity transactionEntity = transactionEntityOptional.get();

      if (Objects.equals(transactionEntity.getStatus(), TransactionStatus.COMPLETED)) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_016", null));
      }
      if (! Objects.equals(request.getTransactionData(), transactionEntity.getRequest())) {
        throw new BusinessException(Utils.makeResponse("CDBUSER_MESSAGE_015", null));
      }

      if (status.equals(RequestStatus.CONFIRMED.name()) || status.equals(RequestStatus.APPROVED.name())) {
        request.setTransactionData(resetPasswordCallback.getResetPasswordCallback());
        Response result = restHandler.postToKeycloakResetPassword(request);
        if (result.isSuccess()) {
          transactionRepository.save(transactionEntity.setStatus(TransactionStatus.COMPLETED));
        } else {
          transactionRepository.save(transactionEntity.setStatus(TransactionStatus.FAILED));
        }
      }
      return new Response(true).setData(status);
  }
}
