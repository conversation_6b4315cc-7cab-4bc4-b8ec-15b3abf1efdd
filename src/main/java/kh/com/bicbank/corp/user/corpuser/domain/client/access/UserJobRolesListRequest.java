package kh.com.bicbank.corp.user.corpuser.domain.client.access;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserJobRolesListRequest {

    @JsonProperty("userJobRolesList")
    private List<UserJobRolesListDto> userJobRolesList;
}
