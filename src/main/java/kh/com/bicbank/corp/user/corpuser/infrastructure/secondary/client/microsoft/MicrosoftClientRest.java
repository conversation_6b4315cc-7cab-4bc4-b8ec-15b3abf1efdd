package kh.com.bicbank.corp.user.corpuser.infrastructure.secondary.client.microsoft;

import com.bic.efw.core.rest.RestTemplateCommon;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftClient;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftListUserClientResponseDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftTokenClientRequestDto;
import kh.com.bicbank.corp.user.corpuser.domain.client.microsoft.MicrosoftTokenClientResponseDto;
import kh.com.bicbank.corp.user.wire.config.MicrosoftProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class MicrosoftClientRest implements MicrosoftClient {
    private final RestTemplateCommon restTemplateCommon;
    private final MicrosoftProperties microsoftProperties;

    @Override
    public MicrosoftTokenClientResponseDto getToken(MicrosoftTokenClientRequestDto microsoftClientRequestDto) {
        log.info("Send request get token Microsoft ");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        String url = microsoftProperties.getUrl().concat("/"+microsoftProperties.getTenant()).concat("/oauth2/token");
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("client_id", microsoftClientRequestDto.getClient_id());
        map.add("client_secret", microsoftClientRequestDto.getClient_secret());
        map.add("resource", microsoftClientRequestDto.getResource());
        map.add("grant_type", microsoftClientRequestDto.getGrant_type());
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, headers);
        ResponseEntity<MicrosoftTokenClientResponseDto> response =
                restTemplateCommon.exchange(url,
                        HttpMethod.POST,
                        entity, new ParameterizedTypeReference<MicrosoftTokenClientResponseDto>(){});
        return response.getBody();
    }



    @Override
    public MicrosoftListUserClientResponseDto getUsers(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer "+accessToken);
        String url = microsoftProperties.getResource().concat("/v1.0/users/?$select=displayName,givenName,jobTitle,mail,mobilePhone,officeLocation,preferredLanguage,surname,userPrincipalName,department,accountEnabled,id,createdDateTime,employeeId");
        HttpEntity<Object> entity = new HttpEntity<>(headers);
        ResponseEntity<MicrosoftListUserClientResponseDto> response =
                restTemplateCommon.exchange(url,
                        HttpMethod.GET,
                        entity, new ParameterizedTypeReference<MicrosoftListUserClientResponseDto>(){});
        return response.getBody();
    }
}
