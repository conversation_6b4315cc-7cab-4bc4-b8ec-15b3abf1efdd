package kh.com.bicbank.corp.user.corpuser.infrastructure.primary.user;

import com.bic.efw.core.payload.Response;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import kh.com.bicbank.corp.user.corpuser.application.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Tag(name = "Users Internal API")
@Hidden
@RequestMapping("/api/internal/v1/bep/users")
@RequiredArgsConstructor
public class InternalBepUserResource {

    private final UserService userService;

    @ApiResponses({
        @ApiResponse(responseCode = "200"
            , content = { @Content(schema = @Schema(implementation = RestUser.class)
            , mediaType = "application/json") }),
        @ApiResponse(responseCode = "404"
            , content = { @Content(schema = @Schema(implementation = Error.class)
            , mediaType = "application/json") })
    })
    @GetMapping("/{userId}")
    ResponseEntity<Response> getUserDetail(@PathVariable String userId) {
        return ResponseEntity.ok(userService.getBepUserDetail(userId));
    }
}
