CREATE TYPE user_status AS ENUM ('ACTIVE','INACTIVE', 'SUSPENDED');

--drop type user_status;
--
drop table public.cdb_user;


CREATE TABLE public.cdb_user (
  id varchar(40) PRIMARY KEY NOT NULL,
  customer_id varchar(40) DEFAULT NULL,
  idp_user_id varchar(50) NOT NULL,
  status varchar(15) DEFAULT 'ACTIVE' NOT NULL,
  created_by varchar(255) DEFAULT NULL,
  created_date timestamp without time zone DEFAULT NULL,
  last_modified_by varchar(255) DEFAULT NULL,
  last_modified_date timestamp without time zone DEFAULT NULL,
  code varchar(255) DEFAULT null
);

comment on column public.cdb_user.id is 'UUID';
comment on column public.cdb_user.status is 'SUSPENDED: disable on keycloak, INACTIVE: remove + disable';

select *
from public.cdb_user
where idp_user_id  = '1e40f96f-1bdb-40ed-b475-42d359179983';

SELECT u FROM cdb_user u WHERE u.idp_user_id  = '1e40f96f-1bdb-40ed-b475-42d359179983';

insert into public.user (ast_modified_by,codeo,create_by,create_date,customer_no,idp_user_id,last_modified_date,status,id) 
values (NULL,NULL,NULL,NULL,'456','f5bf3ab3-d744-45a6-8e44-3c4ba8f5b67c',NULL,'ACTIVE',1);

insert into cdb_user (code,created_by,created_date,customer_id,idp_user_id,last_modified_by,last_modified_date,status,id) 
values (NULL,NULL,NULL,NULL,'1e40f96f-1bdb-40ed-b475-42d359179983',NULL,NULL,'ACTIVE','9de912a7-56fc-4fee-9d9f-2cbd7573bf9d');
