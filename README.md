# Corp User

## Prerequisites

### Java

You need to have Java 17:

- [JDK 17](https://openjdk.java.net/projects/jdk/17/)

## Documentation

- [Assertions](documentation/assertions.md)
- [Hexagonal architecture](documentation/hexagonal-architecture.md)
- [Logs Spy](documentation/logs-spy.md)
- [CORS configuration](documentation/cors-configuration.md)
- [Dev tools](documentation/dev-tools.md)
- [sonar](documentation/sonar.md)
- [Postgresql](documentation/postgresql.md)
- [Cucumber](documentation/cucumber.md)
- [Cucumber authentication](documentation/cucumber-authentication.md)
- [Kipe expression](documentation/kipe-expression.md)
- [Kipe authorization](documentation/kipe-authorization.md)
- [Dummy](documentation/dummy.md)
