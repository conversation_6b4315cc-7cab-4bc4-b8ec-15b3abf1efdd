<?xml version="1.0" encoding="UTF-8" ?>
<project
  xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
>
  <modelVersion>4.0.0</modelVersion>

  <groupId>kh.com.bicbank</groupId>
  <artifactId>corp-user</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>corp-user</name>
  <description>Customer Profile Application</description>
  <packaging>jar</packaging>

  <properties>
    <java.version>17</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <maven.version>3.9.5</maven.version>
    <junit-jupiter.version>5.10.0</junit-jupiter.version>
    <assertj.version>3.24.2</assertj.version>
    <mockito.version>5.6.0</mockito.version>
    <jacoco.version>0.8.11</jacoco.version>
    <maven-enforcer-plugin.version>3.4.1</maven-enforcer-plugin.version>
    <compiler-plugin.version>3.11.0</compiler-plugin.version>
    <surefire-plugin.version>3.2.1</surefire-plugin.version>
    <failsafe-plugin.version>3.0.0-M5</failsafe-plugin.version>
    <commons-lang3.version>3.13.0</commons-lang3.version>
    <spring-boot.version>3.1.5</spring-boot.version>
    <reflections.version>0.10.2</reflections.version>
    <archunit-junit5.version>1.1.0</archunit-junit5.version>
    <git-commit-id-plugin.version>7.0.0</git-commit-id-plugin.version>
    <checksum-maven-plugin.version>1.11</checksum-maven-plugin.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <sonar-maven-plugin.version>3.10.0.2594</sonar-maven-plugin.version>
    <properties-maven-plugin.version>1.2.1</properties-maven-plugin.version>
    <testcontainers.version>1.17.6</testcontainers.version>
    <kafka-clients.version>3.2.0</kafka-clients.version>
    <springdoc-openapi-starter-webmvc.version>2.2.0</springdoc-openapi-starter-webmvc.version>
    <cucumber.version>7.14.0</cucumber.version>
    <json-web-token.version>0.12.3</json-web-token.version>
    <efw.version>1.0.49-SNAPSHOT</efw.version>
    <efw-core.version>1.0.51-SNAPSHOT</efw-core.version>
    <liquibase.version>4.24.0</liquibase.version>
    <modelmapper.version>2.3.0</modelmapper.version>
  </properties>

  <repositories>
    <repository>
      <id>milestone</id>
      <url>https://repo.spring.io/milestone/</url>
    </repository>
    <repository>
      <id>maven2</id>
      <url>https://repo.maven.apache.org/maven2/</url>
    </repository>
    <repository>
      <id>iTech Release Repository</id>
      <url>https://nexus.int.itech.asia/repository/maven-public/</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons-lang3.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-devtools</artifactId>
      <scope>runtime</scope>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>com.bic.efw</groupId>
      <artifactId>efw-core</artifactId>
      <version>${efw-core.version}</version>
    </dependency>

    <dependency>
      <groupId>com.bic.efw</groupId>
      <artifactId>efw-logging</artifactId>
      <version>${efw.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <version>${springdoc-openapi-starter-webmvc.version}</version>
    </dependency>

    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
      <version>${springdoc-openapi-starter-webmvc.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.20</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
      <version>${reflections.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.tngtech.archunit</groupId>
      <artifactId>archunit-junit5-api</artifactId>
      <version>${archunit-junit5.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-suite</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>${json-web-token.version}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-impl</artifactId>
      <version>${json-web-token.version}</version>
      <scope>runtime</scope>
    </dependency>

    <dependency>
      <groupId>org.modelmapper</groupId>
      <artifactId>modelmapper</artifactId>
      <version>${modelmapper.version}</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>${json-web-token.version}</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-spring-boot-starter</artifactId>
      <version>21.1.2</version>
    </dependency>
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-admin-client</artifactId>
      <version>24.0.5</version>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-api</artifactId>
      <version>0.12.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.liquibase</groupId>
      <artifactId>liquibase-core</artifactId>
      <version>${liquibase.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-oauth2-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
    </dependency>
    <dependency>
      <groupId>io.hypersistence</groupId>
      <artifactId>hypersistence-utils-hibernate-60</artifactId>
      <version>3.6.0</version>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>config/*.properties</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <excludes>
          <exclude>config/*.properties</exclude>
        </excludes>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>${jacoco.version}</version>
          <executions>
            <execution>
              <id>pre-unit-tests</id>
              <goals>
                <goal>prepare-agent</goal>
              </goals>
            </execution>
            <!-- Ensures that the code coverage report for unit tests is created after unit tests have been run -->
            <execution>
              <id>post-unit-test</id>
              <phase>test</phase>
              <goals>
                <goal>report</goal>
              </goals>
            </execution>
            <execution>
              <id>pre-integration-tests</id>
              <goals>
                <goal>prepare-agent-integration</goal>
              </goals>
            </execution>
            <!-- Ensures that the code coverage report for integration tests is created after integration tests have been run -->
            <execution>
              <id>post-integration-tests</id>
              <phase>post-integration-test</phase>
              <goals>
                <goal>report-integration</goal>
              </goals>
            </execution>
            <execution>
              <id>merge</id>
              <phase>verify</phase>
              <goals>
                <goal>merge</goal>
              </goals>
              <configuration>
                <fileSets>
                  <fileSet implementation="org.apache.maven.shared.model.fileset.FileSet">
                    <directory>${project.basedir}</directory>
                    <includes>
                      <include>**/*.exec</include>
                    </includes>
                  </fileSet>
                </fileSets>
                <destFile>target/jacoco/allTest.exec</destFile>
              </configuration>
            </execution>
            <execution>
              <id>post-merge-report</id>
              <phase>verify</phase>
              <goals>
                <goal>report</goal>
              </goals>
              <configuration>
                <dataFile>target/jacoco/allTest.exec</dataFile>
                <outputDirectory>target/jacoco/</outputDirectory>
              </configuration>
            </execution>
            <execution>
              <id>check</id>
              <goals>
                <goal>check</goal>
              </goals>
              <configuration>
                <dataFile>target/jacoco/allTest.exec</dataFile>
                <rules>
                  <rule>
                    <element>CLASS</element>
                    <limits>
                      <limit>
                        <counter>LINE</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>1.00</minimum>
                      </limit>
                      <limit>
                        <counter>BRANCH</counter>
                        <value>COVEREDRATIO</value>
                        <minimum>1.00</minimum>
                      </limit>
                    </limits>
                  </rule>
                </rules>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
          <executions>
            <execution>
              <id>enforce-versions</id>
              <goals>
                <goal>enforce</goal>
              </goals>
            </execution>
            <execution>
              <id>enforce-dependencyConvergence</id>
              <configuration>
                <rules>
                  <DependencyConvergence />
                </rules>
                <fail>false</fail>
              </configuration>
              <goals>
                <goal>enforce</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <rules>
              <requireMavenVersion>
                <message>You are running an older version of Maven. JHipster requires at least Maven ${maven.version}</message>
                <version>[${maven.version},)</version>
              </requireMavenVersion>
              <requireJavaVersion>
                <message>You are running an incompatible version of Java. JHipster supports JDK 17.</message>
                <version>[17,18)</version>
              </requireJavaVersion>
            </rules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${spring-boot.version}</version>
          <executions>
            <execution>
              <goals>
                <goal>repackage</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>io.github.git-commit-id</groupId>
          <artifactId>git-commit-id-maven-plugin</artifactId>
          <version>${git-commit-id-plugin.version}</version>
          <executions>
            <execution>
              <goals>
                <goal>revision</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <failOnNoGitDirectory>false</failOnNoGitDirectory>
            <failOnUnableToExtractRepoInfo>false</failOnUnableToExtractRepoInfo>
            <generateGitPropertiesFile>true</generateGitPropertiesFile>
            <includeOnlyProperties>
              <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
              <includeOnlyProperty>^git.commit.id.describe$</includeOnlyProperty>
              <includeOnlyProperty>^git.branch$</includeOnlyProperty>
            </includeOnlyProperties>
            <verbose>false</verbose>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.sonarsource.scanner.maven</groupId>
          <artifactId>sonar-maven-plugin</artifactId>
          <version>${sonar-maven-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire-plugin.version}</version>
        <configuration>
          <!-- Force alphabetical order to have a reproducible build -->
          <runOrder>alphabetical</runOrder>
          <excludes>
            <exclude>**/*IT*</exclude>
            <exclude>**/*CucumberTest*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${failsafe-plugin.version}</version>
        <configuration>
          <!-- Due to spring-boot repackage, without adding this property test classes are not found
               See https://github.com/spring-projects/spring-boot/issues/6254 -->
          <classesDirectory>${project.build.outputDirectory}</classesDirectory>
          <!-- Force alphabetical order to have a reproducible build -->
          <runOrder>alphabetical</runOrder>
          <includes>
            <include>**/*IT*</include>
            <include>**/*CucumberTest*</include>
          </includes>
        </configuration>
        <executions>
          <execution>
            <id>integration-test</id>
            <goals>
              <goal>integration-test</goal>
            </goals>
          </execution>
          <execution>
            <id>verify</id>
            <goals>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>io.github.git-commit-id</groupId>
        <artifactId>git-commit-id-maven-plugin</artifactId>
      </plugin>

<!--      <plugin>-->
<!--        <groupId>org.codehaus.mojo</groupId>-->
<!--        <artifactId>properties-maven-plugin</artifactId>-->
<!--        <version>${properties-maven-plugin.version}</version>-->
<!--        <executions>-->
<!--          <execution>-->
<!--            <phase>initialize</phase>-->
<!--            <goals>-->
<!--              <goal>read-project-properties</goal>-->
<!--            </goals>-->
<!--            <configuration>-->
<!--              <files>-->
<!--                <file>sonar-project.properties</file>-->
<!--              </files>-->
<!--            </configuration>-->
<!--          </execution>-->
<!--        </executions>-->
<!--      </plugin>-->
    </plugins>
    <defaultGoal>spring-boot:run</defaultGoal>
  </build>


</project>
