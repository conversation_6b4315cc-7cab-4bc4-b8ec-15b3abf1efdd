include:
  - project: "devops/centralized-pipeline"
    ref: "feature/itech-cdb"
    file: "gitlab-ci/java/17/maven/cdb-v01/ci-template.yaml"

dev:
  extends:
    - .build-template
  before_script:
    - ECR_ACCOUNT_ID=************
    - DOCKER_REGISTRY_PASSWORD=$(aws ecr get-login-password --region $AWS_REGION)
    - |
      FILE_TO_REPALCE=$(echo -e "FILE_1=Dockerfile
      \n")
    - |
      PIPELINE_ENV=$(echo -e "CI_BUILD_TIME=((CI_BUILD_TIME))
      CI_COMMIT_SHORT_SHA=((CI_COMMIT_SHORT_SHA))
      CI_JOB_ID=((CI_JOB_ID))
      CI_PIPELINE_ID=((CI_PIPELINE_ID))
      SLEEP_TIME_BEFORE_VERIFY=sleep 15s
      DOCKER_REGISTRY=$ECR_ACCOUNT_ID.dkr.ecr.ap-southeast-1.amazonaws.com
      DOCKER_IMAGE=dev-corp-user
      DOCKER_REGISTRY_USERNAME=AWS
      APP_CODE_NAME=corp-user
      \n")
  only:
    - develop
